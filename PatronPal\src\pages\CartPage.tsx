/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useMemo, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Swal from 'sweetalert2';
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  ArrowLeft,
  Receipt,
  Truck,
  Store
} from 'lucide-react';

// Import cart slice actions and selectors
import {
  updateItemQuantity,
  removeItem,
  clearCart,
  updateItemModifiers,
  selectCartItems,
  selectCartItemCount,
  selectCartTotal,
  selectCartSubtotal,
  selectCartTaxAmount,
  selectCartTaxPercentage,
  recalculateTaxes,
  type CartItem
} from '../redux-store/slices/cartSlice';

// Import authentication selectors
import { selectIsAuthenticated, selectCurrentCustomer } from '../redux-store/slices/customerSlice';


interface CartPageProps {
  userId: string;
  onNavigateBack?: () => void;
  onNavigateHome?: () => void;
}

// Individual Cart Item Component
interface CartItemCardProps {
  item: CartItem;
  onUpdateQuantity: (id: string, quantity: number) => void;
  onRemove: (id: string) => void;
  onEditModifiers: (id: string, modifiers: any, note?: string) => void;
}

const CartItemCard: React.FC<CartItemCardProps> = ({
  item,
  onUpdateQuantity,
  onRemove }) => {
  const getItemPrice = () => {
    const basePrice = item.discountPrice || item.price;
    const modifierPrice = item.modifierPrice || 0;
    return basePrice + modifierPrice;
  };

  const getItemTotal = () => {
    return getItemPrice() * item.quantity;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100">
      <div className="relative p-3">
        <img
          src={item.image || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=300&fit=crop'}
          alt={item.name}
          className="w-full h-40 object-cover rounded-lg"
        />

        {/* Remove button */}
        <button
          onClick={() => onRemove(item._id)}
          className="absolute top-5 right-5 bg-red-500 text-white rounded-full p-2 shadow-lg hover:bg-red-600 transition-colors"
        >
          <Trash2 size={16} />
        </button>
      </div>

      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
        <p className="text-gray-600 text-sm mb-3">{item.cuisine}</p>

        {/* Modifiers */}
        {item.modifiers && Object.keys(item.modifiers).length > 0 && (
          <div className="mb-3">
            <div className="text-xs text-gray-600 mb-1">Customizations:</div>
            {Object.entries(item.modifiers)
              .filter(([key, _]) => key !== "note" && key !== "totalPrice") // 🔥 filter added
              .map(([key, modifier]: [string, any]) => (
                <div
                  key={key}
                  className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded mr-1 mb-1 inline-block"
                >
                  {modifier?.name || key}{" "}
                  {modifier?.price > 0 && `(+$${modifier.price.toFixed(2)})`}
                </div>
              ))}
          </div>
        )}


        {/* Note */}
        {item.note && (
          <p className="text-xs text-green-600 mb-3 bg-green-50 p-2 rounded">
            Note: {item.note}
          </p>
        )}

        {/* Price */}
        <div className="mb-4">
          <span className="text-lg font-semibold text-gray-900">
            ${getItemPrice().toFixed(2)}
          </span>
          {item.discountPrice && (
            <span className="ml-2 text-gray-400 line-through text-sm">
              ${(item.price + (item.modifierPrice || 0)).toFixed(2)}
            </span>
          )}
        </div>

        {/* Quantity Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center border border-gray-300 rounded-lg">
            <button
              onClick={() => onUpdateQuantity(item._id, item.quantity - 1)}
              className="p-2 hover:bg-gray-50 rounded-l-lg disabled:opacity-50"
              disabled={item.quantity <= 1}
            >
              <Minus className="h-4 w-4 text-gray-600" />
            </button>
            <span className="px-4 py-2 font-medium text-gray-900 min-w-[3rem] text-center">
              {item.quantity}
            </span>
            <button
              onClick={() => onUpdateQuantity(item._id, item.quantity + 1)}
              className="p-2  rounded-r-lg bg-orange-500 text-white"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>

          {/* Item Total */}
          <div className="text-sm font-medium text-gray-900">
            ${getItemTotal().toFixed(2)}
          </div>
        </div>
      </div>
    </div>
  );
};

const CartPage: React.FC<CartPageProps> = ({

  userId = 'user123',
  onNavigateBack = () => console.log('Navigate back'),
  onNavigateHome = () => console.log('Navigate home')
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
onNavigateBack = () => navigate(-1); 
onNavigateHome = () => navigate("/all-restaurants"); 
  // Use Redux selectors
  const cartItems = useSelector(selectCartItems);
  const cartItemCount = useSelector(selectCartItemCount);

  // Authentication state
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const currentCustomer = useSelector(selectCurrentCustomer);

  // Get service type from URL params, default to 'Delivery'
  const serviceTypeFromUrl = searchParams.get('type') as 'Delivery' | 'Pickup' | null;
  const [serviceType, setServiceType] = useState<'Delivery' | 'Pickup'>(
    serviceTypeFromUrl || 'Delivery'
  );
  const [isLoading, setIsLoading] = useState(false);

  // Update URL when service type changes
  useEffect(() => {
    const currentParams = new URLSearchParams(searchParams);
    currentParams.set('type', serviceType);
    setSearchParams(currentParams, { replace: true });
  }, [serviceType, searchParams, setSearchParams]);

  // Group items by restaurant
  const groupedItems = useMemo(() => {
    return cartItems.reduce((groups: any, item: CartItem) => {
      const restaurantId = item.restaurant?.id || item.restaurantId || 'unknown';
      const restaurantName = item.restaurant?.name || 'Unknown Restaurant';

      if (!groups[restaurantId]) {
        groups[restaurantId] = {
          restaurantName,
          restaurantImage: item.restaurant?.image || 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop',
          items: []
        };
      }
      groups[restaurantId].items.push(item);
      return groups;
    }, {});
  }, [cartItems]);

  // Get tax calculations from Redux store with proper type safety
  const subtotal = useSelector(selectCartSubtotal);
  const taxAmount = useSelector(selectCartTaxAmount);
  const taxPercentage = useSelector(selectCartTaxPercentage);
  const totalWithTax = useSelector(selectCartTotal);

  const calculations = {
    subtotal: Number((subtotal || 0).toFixed(2)),
    taxAmount: Number((taxAmount || 0).toFixed(2)),
    taxPercentage: Number((taxPercentage || 0).toFixed(2)),
    total: Number((totalWithTax || 0).toFixed(2))
  };

  // Listen for tax data updates and recalculate cart totals
  useEffect(() => {
    const handleTaxUpdate = () => {
      dispatch(recalculateTaxes());
    };

    window.addEventListener('taxDataUpdated', handleTaxUpdate);

    return () => {
      window.removeEventListener('taxDataUpdated', handleTaxUpdate);
    };
  }, [dispatch]);

  // Redux action handlers
  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      dispatch(removeItem(itemId));
    } else {
      const currentItem = cartItems.find((item: CartItem) => item._id === itemId);
      if (currentItem) {
        const action = newQuantity > currentItem.quantity ? "increase" : "decrease";
        dispatch(updateItemQuantity({ id: itemId, action }));
      }
    }
  };

  const handleRemoveItem = (itemId: string) => {
    dispatch(removeItem(itemId));
  };

  const handleEditModifiers = (itemId: string, modifiers: any, note?: string) => {
    dispatch(updateItemModifiers({ id: itemId, modifiers, note }));
  };

  const handleServiceTypeChange = (type: 'Delivery' | 'Pickup') => {
    setServiceType(type);
  };

  const handleProceedToPayment = () => {
    if (cartItems.length === 0) return;

    // Check authentication first
    if (!isAuthenticated || !currentCustomer) {
      // Show SweetAlert2 warning for unauthenticated users
      Swal.fire({
        title: 'Login Required',
        html: `
          <div style="text-align: center;">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0VjVIMTBWNEMxMCAyLjkgMTAuOSAyIDEyIDJaTTIxIDZWMjBDMjEgMjEuMSAyMC4xIDIyIDE5IDIySDVDMy45IDIyIDMgMjEuMSAzIDIwVjZIMjFaTTEwIDEwVjE4SDEyVjEwSDEwWk0xNCAxMFYxOEgxNlYxMEgxNFoiIGZpbGw9IiNGRjVDMDAiLz4KPC9zdmc+Cg==" alt="Login Required" style="width: 64px; height: 64px; margin-bottom: 16px;">
            <p style="font-size: 16px; color: #333; margin: 0;">Please login to proceed with your order</p>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Login Now',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#FF5C00',
        cancelButtonColor: '#6c757d',
        customClass: {
          popup: 'swal2-popup-custom',
          title: 'swal2-title-custom',
          confirmButton: 'swal2-confirm-custom',
          cancelButton: 'swal2-cancel-custom'
        }
      }).then((result) => {
        if (result.isConfirmed) {
          // Redirect to login with current checkout URL as redirect parameter
          const checkoutUrl = `/checkout?type=${serviceType}`;
          navigate(`/login?redirect=${encodeURIComponent(checkoutUrl)}`);
        }
      });
      return;
    }

    setIsLoading(true);

    const estimatedTime = serviceType === 'Delivery' ? '30-45 min' : '15-20 min';

    const orderData = {
      userId,
      items: cartItems,
      serviceType,
      totals: calculations,
      estimatedTime
    };

    // Navigate to checkout page with service type in URL
    setTimeout(() => {
      console.log('Proceeding to payment with order:', orderData);
      setIsLoading(false);
      navigate(`/checkout?type=${serviceType}`);
    }, 1000);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-20">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto"></div>
            <p className="mt-6 text-lg text-gray-600">Proceeding to payment...</p>
          </div>
        </div>
      </div>
    );
  }

  // Empty cart state
  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <button
              onClick={onNavigateBack}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Your Cart</h1>
          </div>

          {/* Empty Cart */}
          <div className="text-center py-20">
            <div className="bg-white rounded-full p-8 w-32 h-32 mx-auto mb-6 shadow-sm">
              <ShoppingCart className="h-16 w-16 text-gray-300 mx-auto" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-3">Your cart is empty</h2>
            <p className="text-gray-600 mb-8 text-lg">
              Add some delicious items to get started!
            </p>
            <button
              onClick={onNavigateHome}
              className="bg-orange-500 text-white px-8 py-4 rounded-lg hover:bg-orange-600 transition-colors font-semibold text-lg"
            >
              Browse Menu
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={onNavigateBack}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <div className="flex items-center">
              <ShoppingCart className="h-7 w-7 mr-3 text-gray-700" />
              <h1 className="text-3xl font-bold text-gray-900">Your Cart</h1>
              <span className="ml-3 bg-orange-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                {cartItemCount} item{cartItemCount !== 1 ? 's' : ''}
              </span>
            </div>
          </div>

          {/* Clear Cart Button */}
          <button
            onClick={() => dispatch(clearCart())}
            className="text-red-600 hover:text-red-700 font-medium text-sm flex items-center"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Clear Cart
          </button>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="xl:col-span-2 space-y-8">
            {Object.entries(groupedItems).map(([restaurantId, group]: [string, any]) => (
              <div key={restaurantId} className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                {/* Restaurant Header */}
                <div className="flex items-center mb-6 pb-4 border-b border-gray-100">
                  <img
                    src={group.restaurantImage}
                    alt={group.restaurantName}
                    className="w-16 h-16 object-cover rounded-lg mr-4"
                  />
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">
                      {group.restaurantName}
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">
                      {group.items.length} item{group.items.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>

                {/* Items Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {group.items.map((item: CartItem) => (
                    <CartItemCard
                      key={item._id}
                      item={item}
                      onUpdateQuantity={handleUpdateQuantity}
                      onRemove={handleRemoveItem}
                      onEditModifiers={handleEditModifiers}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="xl:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 sticky top-8">
              <h3 className="font-bold text-xl mb-6 flex items-center text-gray-900">
                <Receipt className="h-6 w-6 mr-3" />
                Order Summary
              </h3>

              {/* Service Type Selection */}
              <div className="mb-6">
                <h4 className="font-semibold mb-4 text-gray-900">Service Type</h4>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => handleServiceTypeChange('Delivery')}
                    className={`flex flex-col items-center p-4 rounded-lg border-2 transition-all ${serviceType === 'Delivery'
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                      }`}
                  >
                    <Truck className="h-6 w-6 mb-2" />
                    <span className="font-medium">Delivery</span>
                    <span className="text-xs mt-1">30-45 min</span>
                  </button>
                  <button
                    onClick={() => handleServiceTypeChange('Pickup')}
                    className={`flex flex-col items-center p-4 rounded-lg border-2 transition-all ${serviceType === 'Pickup'
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                      }`}
                  >
                    <Store className="h-6 w-6 mb-2" />
                    <span className="font-medium">Pickup</span>
                    <span className="text-xs mt-1">15-20 min</span>
                  </button>
                </div>
              </div>

              {/* Order Breakdown */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-gray-700">
                  <span>Subtotal ({cartItemCount} items)</span>
                  <span>${calculations.subtotal.toFixed(2)}</span>
                </div>

                {/* Tax Information */}
                {calculations.taxPercentage > 0 && (
                  <div className="flex justify-between text-gray-700">
                    <span>Tax ({calculations.taxPercentage}%)</span>
                    <span>${calculations.taxAmount.toFixed(2)}</span>
                  </div>
                )}
              </div>

              <div className="border-t border-gray-200 pt-4 mb-6">
                <div className="flex justify-between font-bold text-xl text-gray-900">
                  <span>Total</span>
                  <span>${calculations.total.toFixed(2)}</span>
                </div>
              </div>

              {/* Proceed to Payment Button */}
              <button
                onClick={handleProceedToPayment}
                disabled={isLoading || cartItems.length === 0}
                className="w-full bg-orange-500 text-white py-4 rounded-lg hover:bg-orange-600 transition-colors font-bold text-lg disabled:opacity-50 shadow-lg"
              >
                {isLoading ? 'Processing...' : `Proceed to Payment `}
              </button>

            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default CartPage;