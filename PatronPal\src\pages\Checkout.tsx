/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import type { AppDispatch } from '../redux-store/store';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { selectCartItems, selectCartTotal, clearCart } from '../redux-store/slices/cartSlice';
import { selectCurrentCustomer } from '../redux-store/slices/customerSlice';
import { setCurrentOrder } from '../redux-store/slices/orderTrackingSlice';
import {
    createParentOnlineOrder,
    selectParentOnlineOrderLoading,
    selectParentOnlineOrderError,
    selectCreateOrderResponse,
    clearCreateOrderResponse,
    clearError as clearParentOrderError
} from '../redux-store/slices/parentOnlineOrderSlice';
import {
    createOnlineOrder,
    selectLoading as selectSubOrderLoading,
    selectError as selectSubOrderError,
    clearError as clearSubOrderError
} from '../redux-store/slices/subOnlineOrderitemSlice';
import {
    calculatePriceWithTax,
    getStoredTaxPercentage
} from '../utils/taxUtils';
import CartSummary from '../components/CartSummary';
import CheckoutForm from '../components/CheckoutForm';

const Checkout: React.FC = () => {
    // Get current customer from Redux store
    const currentCustomer = useSelector(selectCurrentCustomer);

    const [firstName, setFirstName] = useState(currentCustomer?.fname);
    const [lastName, setLastName] = useState(currentCustomer?.lname);
    const [mobileNumber, setMobileNumber] = useState(currentCustomer?.Phone);
    const [email, setEmail] = useState(currentCustomer?.Email);
    const [selectedMethod, setSelectedMethod] = useState('');
    const [orderTypes, setOrderTypes] = useState('');
    const [showSuccessAlert, setShowSuccessAlert] = useState(false);
    const [isProcessingOrder, setIsProcessingOrder] = useState(false);

    useEffect(() => {
        setFirstName(prev => prev || currentCustomer?.fname || '');
        setLastName(prev => prev || currentCustomer?.lname || '');
        setMobileNumber(prev => prev || currentCustomer?.Phone || '');
        setEmail(prev => prev || currentCustomer?.Email || '');
    }, []);

    const dispatch = useDispatch<AppDispatch>();
    const navigate = useNavigate();

    // Get cart data from Redux store
    const cartItems = useSelector(selectCartItems);
    const cartTotal = useSelector(selectCartTotal);

    // Get order state from Redux store
    const isParentOrderLoading = useSelector(selectParentOnlineOrderLoading);
    const parentOrderError = useSelector(selectParentOnlineOrderError);
    const createOrderResponse = useSelector(selectCreateOrderResponse);

    // Get sub-order state from Redux store
    const isSubOrderLoading = useSelector(selectSubOrderLoading);
    const subOrderError = useSelector(selectSubOrderError);

    const userId = localStorage.getItem('userid') || '';

    // Get URL parameter on component mount
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const type = urlParams.get('type') || '';
        console.log('Type from URL:', type);
        // Convert to lowercase and ensure it's a valid enum value
        const normalizedType = type.toLowerCase();
        if (normalizedType === 'delivery' || normalizedType === 'pickup') {
            setOrderTypes(normalizedType);
        } else {
            // Default to delivery if no valid type is provided
            setOrderTypes('delivery');
        }
    }, []);

    // Handle successful order creation
    useEffect(() => {
        if (createOrderResponse) {
            setShowSuccessAlert(true);
            setIsProcessingOrder(false);

            // Save order data for tracking before clearing cart
            dispatch(setCurrentOrder({
                items: cartItems as any,
                total: cartTotal,
                paymentMethod: selectedMethod,
                orderType: orderTypes,
                orderId: (createOrderResponse as any)?._id || (createOrderResponse as any)?.id || 'unknown',
            }));

            // Clear cart after successful order
            dispatch(clearCart());

            // Auto-hide alert after 3 seconds and navigate
            setTimeout(() => {
                setShowSuccessAlert(false);
                dispatch(clearCreateOrderResponse());
                // Small delay to ensure order data is saved before navigation
                setTimeout(() => {
                    navigate(`/tracking?type=${selectedMethod}`);
                }, 100);
            }, 3000);
        }
    }, [createOrderResponse, dispatch, navigate, selectedMethod, orderTypes]);

    // Handle parent order errors
    useEffect(() => {
        if (parentOrderError) {
            // alert(`Parent order failed: ${parentOrderError}`);
            console.log(`Parent order failed: ${parentOrderError}`);
            dispatch(clearParentOrderError());
            setIsProcessingOrder(false);
        }
    }, [parentOrderError, dispatch]);

    // Handle sub-order errors
    useEffect(() => {
        if (subOrderError) {
            // alert(`Sub-order failed: ${subOrderError}`);
            console.log(`Sub-order failed: ${subOrderError}`);
            dispatch(clearSubOrderError());
            setIsProcessingOrder(false);
        }
    }, [subOrderError, dispatch]);

    // Helper function to calculate individual item price with tax
    const calculateItemPriceWithTax = (item: any): number => {
        // Calculate base item price including modifiers
        const baseItemPrice = item.discountPrice || item.price;
        let modifierPrice = 0;

        // Calculate total modifier price
        if (item.modifiers) {
            Object.values(item.modifiers).forEach((modifier: any) => {
                if (modifier && typeof modifier.price === "number") {
                    modifierPrice += modifier.price;
                }
            });
        }

        // Total item price before tax = (base price + modifier price) * quantity
        const itemPriceBeforeTax = (baseItemPrice + modifierPrice) * item.quantity;

        // Get tax percentage from localStorage
        const taxPercentage = getStoredTaxPercentage();

        // Calculate price with tax
        const priceWithTax = calculatePriceWithTax(itemPriceBeforeTax, taxPercentage);

        console.log(`Item: ${item.name}`);
        console.log(`Base price: $${baseItemPrice}, Modifier price: $${modifierPrice}, Quantity: ${item.quantity}`);
        console.log(`Price before tax: $${itemPriceBeforeTax.toFixed(2)}`);
        console.log(`Tax percentage: ${taxPercentage}%`);
        console.log(`Tax amount: $${priceWithTax.taxAmount}`);
        console.log(`Total price with tax: $${priceWithTax.totalPrice}`);

        return priceWithTax.totalPrice;
    };

    const handleSubmit = async () => {
        // For now, only handle COD orders
        if (selectedMethod !== 'cod') {
            Swal.fire({
                title: 'Please select Cash on Delivery',
                text: 'Currently only Cash on Delivery payment method is supported',
                icon: 'warning',
                confirmButtonText: 'OK',
                confirmButtonColor: '#FF5C00',
                width: '400px',
                padding: '2rem',
                customClass: {
                    popup: 'swal2-center-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'swal2-confirm-custom'
                },
                backdrop: true,
                allowOutsideClick: true
            });
            return;
        }

        if (!currentCustomer) {
            alert('Customer information is required');
            return;
        }

        if (cartItems.length === 0) {
            alert('Cart is empty');
            return;
        }

        if (!orderTypes || (orderTypes !== 'delivery' && orderTypes !== 'pickup')) {
            alert('Please select a valid order type (delivery or pickup)');
            return;
        }

        setIsProcessingOrder(true);

        try {
            console.log('Starting order creation process...');
            console.log('Order type:', orderTypes);
            console.log('Cart items:', cartItems);
            console.log('User ID:', userId);
            console.log('Customer ID:', currentCustomer?._id);

            // Validate required data
            if (!userId) {
                throw new Error('User ID is required to create an order');
            }

            if (!currentCustomer?._id) {
                throw new Error('Customer information is required to create an order');
            }

            // Step 1: Create a single sub-order with all cart items
            console.log('Creating single sub-order with all items and tax calculations...');

            // Calculate total amount for all items with tax
            const totalAmountWithTax = cartItems.reduce((total: any, item) => {
                return total + calculateItemPriceWithTax(item);
            }, 0);

            console.log(`Total amount with tax for all items: $${totalAmountWithTax.toFixed(2)}`);

            const subOrderData = {
                Status: 'patronpal order',
                orderType: orderTypes.toLowerCase() as 'delivery' | 'pickup',
                orderStatus: 'new order' as const,
                product: cartItems, // All cart items in one sub-order
                customerId: currentCustomer._id!, // Non-null assertion since we validated above
                userId: userId,
                PaymentStatus: 'cash' as const,
                Amount: totalAmountWithTax,
                deliveryfee: orderTypes.toLowerCase() === 'delivery' ? 50 : 0, // Example delivery fee

                // Add delivery options if it's a delivery order
                ...(orderTypes.toLowerCase() === 'delivery' && {
                    deliveryOptions: {
                        dropOffAddress: currentCustomer?.address || '',
                        dropOffInstructions: 'Please call upon arrival',
                        expressDelivery: 'no'
                    }
                }),

                // Add pickup options if it's a pickup order
                ...(orderTypes.toLowerCase() === 'pickup' && {
                    pickUpObj: {
                        pickupAddress: 'Restaurant Address', // Replace with actual restaurant address
                        pickupOptions: {
                            standardPickup: true,
                            schedulePickup: false
                        }
                    }
                })
            };

            console.log('Creating sub-order with data:', subOrderData);

            // Dispatch the create sub-order action and wait for response
            const result = await dispatch(createOnlineOrder(subOrderData));

            if (createOnlineOrder.fulfilled.match(result)) {
                console.log('Sub-order created successfully:', result.payload);
                const createdSubOrder = result.payload;

                // Step 2: Create parent order with sub-order ID
                const subOrderIds = [createdSubOrder._id];

                const parentOrderData = {
                    customerId: currentCustomer._id!, // Non-null assertion since we validated above
                    subOnlineOrderId: subOrderIds,
                    totalAmount: cartTotal,
                    orderType: orderTypes,
                    paymentMethod: selectedMethod,
                    customerDetails: {
                        firstName,
                        lastName,
                        mobileNumber,
                        email,
                        address: orderTypes.toLowerCase() !== 'pickup' ? currentCustomer?.address || '' : null
                    }
                };

                console.log('Creating parent order with data:', parentOrderData);

                // Dispatch the create parent order action
                dispatch(createParentOnlineOrder(parentOrderData));
            } else {
                console.error('Sub-order creation failed:', result.payload);
                throw new Error(`Failed to create sub-order: ${result.payload}`);
            }
        } catch (error) {
            console.error('Error in order creation process:', error);
            alert('Failed to place order. Please try again.');
            setIsProcessingOrder(false);
        }
    };

    // Calculate total loading state
    const isLoading = isParentOrderLoading || isSubOrderLoading || isProcessingOrder;

    return (
        <div className="min-h-screen bg-gray-50 p-5 relative">
            {/* Success Alert */}
            {showSuccessAlert && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
                    <div className="bg-white rounded-2xl p-8 shadow-2xl max-w-md w-full mx-4 text-center">
                        <div className="flex flex-col items-center space-y-6">
                            {/* Success Icon */}
                            <div className="relative">
                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="absolute inset-0 bg-green-200 rounded-full animate-ping opacity-20"></div>
                            </div>

                            {/* Title */}
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">Order placed successfully!</h3>
                                <p className="text-gray-600 text-sm">Redirecting to tracking page...</p>
                            </div>

                            {/* Progress Bar */}
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-green-500 h-2 rounded-full transition-all duration-3000 ease-out" style={{width: '100%'}}></div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Processing Overlay */}
            {isProcessingOrder && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
                    <div className="bg-white rounded-2xl p-8 shadow-2xl max-w-md w-full mx-4 text-center">
                        <div className="flex flex-col items-center space-y-6">
                            {/* Spinner */}
                            <div className="relative">
                                <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="w-8 h-8 bg-blue-500 rounded-full opacity-20 animate-pulse"></div>
                                </div>
                            </div>

                            {/* Title */}
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">Processing your order</h3>
                                <p className="text-gray-600 text-sm">Please wait while we process your order...</p>
                            </div>

                            {/* Progress Bar */}
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{width: '70%'}}></div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="w-full mx-auto flex justify-center items-start gap-6 flex-wrap">
                {/* Main Form Section */}
                <CheckoutForm 
                    currentCustomer={currentCustomer}
                    orderTypes={orderTypes}
                    selectedMethod={selectedMethod}
                    setSelectedMethod={setSelectedMethod}
                    handleSubmit={handleSubmit}
                    isOrderLoading={isLoading}
                />

                {/* Cart Summary Section */}
                <div className="w-[550px] sticky top-20">
                    <h1 className="text-base font-bold mb-2 text-gray-900">Order Summary</h1>
                    <CartSummary />
                </div>
            </div>
        </div>
    );
};

export default Checkout;