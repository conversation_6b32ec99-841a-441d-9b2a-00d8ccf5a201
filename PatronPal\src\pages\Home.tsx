import { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { FiChevronDown } from "react-icons/fi";
import { ShoppingCart } from "lucide-react";
import Swal from 'sweetalert2';
import { assets } from "../assets/assets";
import { useAppSelector, useAppDispatch } from "../redux-store/hooks";
import {
  selectDeliveryOption,
  setDeliveryOption,
  type DeliveryOption,
} from "../redux-store/slices/addressSlice";
import { logout, selectCurrentCustomer } from "../redux-store/slices/customerSlice";
import { selectCartItemCount } from "../redux-store/slices/cartSlice";
import Footer from "../components/Footer";

interface CityData {
  name: string;
  image: string;
}

const Home = () => {
  const selectedOption = useAppSelector(selectDeliveryOption);
  const dispatch = useAppDispatch();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isStateDropdownOpen, setIsStateDropdownOpen] = useState<boolean>(false);
  const [selectedState, setSelectedState] = useState<string>("Select State");
  const [usStates, setUsStates] = useState<string[]>([]);
  const [loadingStates, setLoadingStates] = useState<boolean>(false);
  const [stateError, setStateError] = useState<string>("");
  const [stateSearchQuery, setStateSearchQuery] = useState<string>("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const stateDropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const userId = useAppSelector(
    (state) => state.customer?.currentCustomer?._id
  );

  // Get current customer and cart data
  const currentCustomer = useAppSelector(selectCurrentCustomer);
  const cartItemCount = useAppSelector(selectCartItemCount);

  // Navigation functions
  // const goToFavorites = () => {
  //   navigate('/favorites');
  // };

  const goToCart = () => {
    navigate('/cart');
  };

  const goToProfile = () => {
    navigate('/profile-settings');
  };

  // Filter states based on search query
  const filteredStates = usStates.filter(state =>
    state.toLowerCase().includes(stateSearchQuery.toLowerCase())
  );

  // Function to fetch US states using Google Places API
  const fetchUSStates = async () => {
    if (loadingStates || usStates.length > 0) return;

    setLoadingStates(true);
    try {
      // Predefined list of US states as fallback
      const statesList = [
        "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut",
        "Delaware", "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa",
        "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan",
        "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire",
        "New Jersey", "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio",
        "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota",
        "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia",
        "Wisconsin", "Wyoming"
      ];

      setUsStates(statesList);
      console.log("US States loaded:", statesList);
    } catch (error) {
      console.error("Error fetching US states:", error);
    } finally {
      setLoadingStates(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
      if (
        stateDropdownRef.current &&
        !stateDropdownRef.current.contains(event.target as Node)
      ) {
        setIsStateDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Fetch US states on component mount
  useEffect(() => {
    fetchUSStates();
  }, []);

  const handleSelect = (option: DeliveryOption) => {
    dispatch(setDeliveryOption(option));
    setIsOpen(false);
  };

  const handleStateSelect = (state: string) => {
    setSelectedState(state);
    setIsStateDropdownOpen(false);
    setStateError(""); // Clear error when state is selected
    setStateSearchQuery(""); // Clear search when state is selected
    console.log("Selected state:", state);
  };

  // Handle Find Food button click
  const handleFindFood = () => {
    // Clear any previous error
    setStateError("");

    // Validate that a state is selected
    if (selectedState === "Select State") {
      setStateError("Please select a state to find restaurants");
      return;
    }

    // Use selected state for navigation
    const stateParam = encodeURIComponent(selectedState);

    // Save delivery option to localStorage
    localStorage.setItem("deliveryOption", selectedOption);

    // Save selected state to localStorage
    localStorage.setItem("selectedState", selectedState);

    // Navigate to restaurant list with the selected option and state as query parameters
    navigate(
      `/all-restaurants?type=${selectedOption.toLowerCase()}&state=${stateParam}`
    );
  };

  const handleLogout = () => {
    // Show SweetAlert2 logout confirmation
    Swal.fire({
      title: 'Logging out...',
      text: 'You have been successfully logged out',
      icon: 'success',
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true,
      width: '500px',
      heightAuto: false,
      padding: '3rem',
      customClass: {
        popup: 'swal2-center-custom',
        title: 'swal2-title-custom'
      },
      backdrop: true,
      allowOutsideClick: false,
      didOpen: (popup) => {
        popup.style.minHeight = '300px';
        popup.style.display = 'flex';
        popup.style.flexDirection = 'column';
        popup.style.justifyContent = 'center';
      }
    });

    // Dispatch logout action which will now clear cart and address data
    dispatch(logout());

    // Navigate to login page
    setTimeout(() => {
      navigate("/");
    }, 2000);
  };

  // Handle Find Food by City (now using state instead of address)
  const handleFindFoodbyCity = (cityName: string) => () => {
    // Set the selected state to the city name and navigate
    setSelectedState(cityName);
    const stateParam = encodeURIComponent(cityName);
    navigate(
      `/all-restaurants?type=${selectedOption.toLowerCase()}&state=${stateParam}`
    );
    localStorage.setItem("selectedState", cityName);
  };

  // City data
  const cities: CityData[] = [
    {
      name: "California",
      image: `${assets.california}`,
    },
    {
      name: "New York",
      image:
        "https://images.unsplash.com/photo-1522083165195-3424ed129620?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Texas",
      image:
        "https://images.unsplash.com/photo-1601918774946-25832a4be0d6?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Chicago",
      image: `${assets.Chicago_image}`,
    },
    {
      name: "Georgia",
      image: `${assets.georgia}`,
    },
    {
      name: "Ohio",
      image: `${assets.ohio}`,
    },
    {
      name: "Pennsylvania",
      image: `${assets.pennsylvania}`,
    },
    {
      name: "New Jersey",
      image: `${assets.newJersey}`,
    },
    {
      name: "Tennessee",
      image: `${assets.tennessee}`,
    },
    {
      name: "Washington DC",
      image: `${assets.washingtonDC}`,
    },
    {
      name: "Alaska",
      image: `${assets.alaska}`,
    },
    {
      name: "Mexico",
      image: `${assets.mexico}`,
    },
  ];

  return (
    <div className="min-h-screen flex flex-col bg-black">
      {/* ----Header---- */}

      <header className="sticky top-0 w-full z-50 p-4 md:px-16 px-4 flex justify-between items-center bg-black/26 backdrop-blur-sm">
        <div className="text-white font-bold text-xl">
          Patron<span className="text-primary">Pal</span>
        </div>
        {!userId ? (
          <div className="flex gap-2">
            <Link
              to={"/login"}
              className="bg-primary text-white px-6 py-2 rounded-full"
            >
              Log In
            </Link>
            <Link
              to={"/signup"}
              className="bg-transparent text-white border border-white px-6 py-2 rounded-full"
            >
              Sign Up
            </Link>
          </div>
        ) : (
          <div className="flex items-center gap-4">
            {/* Mobile Profile Avatar */}
            <div className="block md:hidden">
              <button onClick={goToProfile}>
                <img
                  src={
                    currentCustomer?.profile_pic ||
                    'https://www.gstatic.com/images/branding/product/1x/avatar_circle_blue_512dp.png'
                  }
                  alt="User"
                  className="w-9 h-9 rounded-full object-cover border-2 border-white"
                />
              </button>

            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-6">
              {/* Profile Avatar */}
              <button onClick={goToProfile}>
                <img
                  src={
                    currentCustomer?.profile_pic ||
                    'https://www.gstatic.com/images/branding/product/1x/avatar_circle_blue_512dp.png'
                  }
                  alt="User"
                  className="w-9 h-9 rounded-full object-cover border-2 border-white"
                />
              </button>

              {/* Favorites */}
              {/* <button
                onClick={goToFavorites}
                className="flex items-center text-sm text-white hover:text-gray-200 transition-colors"
              >
                <Heart className="w-5 h-5 mr-1" />
                Favorites
              </button> */}

              {/* Cart */}
              <button
                onClick={goToCart}
                className="flex items-center text-sm text-white hover:text-gray-200 transition-colors relative"
              >
                <ShoppingCart className="w-5 h-5 mr-1" />
                Cart
                {cartItemCount > 0 && (
                  <span className="bg-primary text-white text-xs rounded-full px-2 py-1 ml-1 mb-4 min-w-[20px] text-center">
                    {cartItemCount > 99 ? '99+' : cartItemCount}
                  </span>
                )}
              </button>

              {/* Logout */}
              <button
                onClick={handleLogout}
                className="bg-primary hover:bg-orange-600 cursor-pointer text-white px-6 py-2 rounded-full transition-colors"
              >
                Logout
              </button>
            </div>

            {/* Mobile Icons */}
            <div className="flex md:hidden items-center gap-4">
              {/* Favorites */}
              {/* <button onClick={goToFavorites}>
                <Heart className="w-6 h-6 text-white" />
              </button> */}

              {/* Cart with Badge */}
              <button onClick={goToCart} className="relative">
                <ShoppingCart className="w-6 h-6 text-white" />
                {cartItemCount > 0 && (
                  <span className="absolute -top-2 -right-4 bg-primary text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                    {cartItemCount > 99 ? '99+' : cartItemCount}
                  </span>
                )}
              </button>

              {/* Logout */}
              <button
                onClick={handleLogout}
                className="bg-primary hover:bg-orange-600 cursor-pointer text-white px-4 py-2 rounded-full transition-colors text-sm"
              >
                Logout
              </button>
            </div>
          </div>
        )}
      </header>
      <div className="flex-grow">
        {/* ---Hero Section--- */}
        <section className="relative md:h-screen h-[300px]">
          <div className="absolute inset-0 bg-gradient-to-r rounded-b-xl from-black to-transparent z-0"></div>
          <img
            src={assets.banner_one}
            alt="Delicious burger"
            className="absolute inset-0 w-full rounded-b-xl h-full object-cover object-bottom opacity-80"
          />
          <div className="relative md:mt-0 -mt-4 z-10 h-full flex flex-col justify-center px-4 md:px-16 md:max-w-2xl">
            <h1 className="text-white text-2xl md:text-3xl font-[600] mb-0">
              Cravings met, cupboards stocked
              <br />
              Without the trip!
            </h1>
            <div className="absolute md:top-[60%] top-[76%] left-0 right-0 px-4 md:px-16 w-full">
              <div className="flex items-center flex-col sm:flex-row gap-2 mt-0 w-full">
                {/* <div className="relative flex-grow w-full bg-white rounded-md ">
                  <AddressDropdown
                    value={address}
                    onSelectAddress={handleSelectAddress}
                    autoFocus={false}
                  />
                </div> */}
                <div
                  className="relative w-full md:max-w-[200px]"
                  ref={dropdownRef}
                >
                  <button
                    onClick={() => setIsOpen((prev) => !prev)}
                    className="bg-white shadow shadow-gray-300 text-black px-6 py-2 md:py-3 rounded-md flex items-center justify-between w-full"
                  >
                    {selectedOption}
                    <FiChevronDown className="ml-2" />
                  </button>

                  {isOpen && (
                    <ul className="absolute z-10 bg-white shadow-md rounded-md mt-2 w-full">
                      {["Delivery", "Pickup"].map((option) => (
                        <li
                          key={option}
                          onClick={() => handleSelect(option as DeliveryOption)}
                          className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${selectedOption === option
                              ? "font-semibold bg-gray-100"
                              : ""
                            }`}
                        >
                          {option}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                {/* US States Dropdown */}
                <div
                  className="relative w-full md:max-w-[200px]"
                  ref={stateDropdownRef}
                >
                  <button
                    onClick={() => {
                      setIsStateDropdownOpen((prev) => !prev);
                      if (!isStateDropdownOpen) {
                        setStateSearchQuery(""); // Clear search when opening dropdown
                      }
                    }}
                    className="bg-white shadow shadow-gray-300 text-black px-6 py-2 md:py-3 rounded-md flex items-center justify-between w-full"
                    disabled={loadingStates}
                  >
                    {loadingStates ? "Loading..." : selectedState}
                    <FiChevronDown className="ml-2" />
                  </button>

                  {isStateDropdownOpen && !loadingStates && (
                    <div className="absolute z-10 bg-white shadow-md rounded-md mt-2 w-full max-h-64 overflow-hidden">
                      {/* Search Input */}
                      <div className="p-2 border-b border-gray-200">
                        <input
                          type="text"
                          placeholder="Search states..."
                          value={stateSearchQuery}
                          onChange={(e) => setStateSearchQuery(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          autoFocus
                        />
                      </div>

                      {/* States List */}
                      <ul className="max-h-48 overflow-y-auto">
                        {!stateSearchQuery && (
                          <li
                            onClick={() => handleStateSelect("Select State")}
                            className={`px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-500 ${selectedState === "Select State"
                                ? "font-semibold bg-gray-100"
                                : ""
                              }`}
                          >
                            Select State
                          </li>
                        )}
                        {filteredStates.length > 0 ? (
                          filteredStates.map((state) => (
                            <li
                              key={state}
                              onClick={() => handleStateSelect(state)}
                              className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${selectedState === state
                                  ? "font-semibold bg-gray-100"
                                  : ""
                                }`}
                            >
                              {state}
                            </li>
                          ))
                        ) : stateSearchQuery ? (
                          <li className="px-4 py-2 text-gray-500 text-sm">
                            No states found matching "{stateSearchQuery}"
                          </li>
                        ) : null}
                      </ul>
                    </div>
                  )}
                </div>

                <button
                  onClick={handleFindFood}
                  disabled={selectedState === "Select State"}
                  className={`${selectedState === "Select State"
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-primary cursor-pointer hover:bg-orange-600"
                  } text-white px-6 md:py-3 py-2 rounded-full whitespace-nowrap md:w-fit w-full transition-colors`}
                >
                  Find Food
                </button>
              </div>

              {/* Error message */}
              {stateError && (
                <div className="mt-2 text-red-500 text-sm text-center">
                  {stateError}
                </div>
              )}
            </div>
          </div>
        </section>
        {/* --Business Registration Section--- */}
        <div className="relative md:mt-0 mt-22">
          <section className=" relative flex flex-col md:flex-row bg-gray-100 items-center justify-between mt-5 rounded-lg">
            <img
              src={assets.banner_two}
              alt="Chef cooking"
              className="rounded-lg w-full h-80 relative object-cover"
            />
            <div className="absolute bg-white max-w-md m-6 md:p-6 p-2 rounded-xl z-10 shadow-lg md:ml-16 md:-translate-y-12 lg:translate-y-25 translate-y-56 pr-20 pb-4">
              <h2 className="font-bold text-lg mb-2">
                List your Business on{" "}
                <span className="text-primary">PatronPal</span>
              </h2>
              <p className="mb-4 text-sm">
                Joining Patronpal is easy. We'll list your menu and products
                online, help you manage orders, and handle fast, reliable
                delivery — straight to hungry customers in no time.
              </p>
              <p className="text-sm mb-4">
                Ready to partner up? Let's get started today!
              </p>
              <a
                href="http://patronworks.com"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-primary hover:bg-orange-600 text-white font-md py-2.5 px-5 rounded-full"
              >
                Get Started
              </a>
            </div>
          </section>
        </div>
        {/*-- Popular Cities Section--- */}
        <section className="py-25 md:px-16 px-8 md:mt-0 mt-32 ">
          <div className="container mx-auto">
            <h2 className="text-2xl font-bold mb-8">
              Popular in following Cities and growing
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {cities
                .concat(cities, cities)
                .slice(0, 12)
                .map((city, index) => (
                  <div
                    onClick={handleFindFoodbyCity(city.name)}
                    key={index}
                    className="relative h-48 rounded-lg overflow-hidden cursor-pointer group"
                  >
                    <img
                      src={city.image}
                      alt={city.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
                    <div className="absolute top-4 left-4 text-white font-semibold text-lg">
                      {city.name}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </section>
      </div>
      <div>
        <Footer />
      </div>
    </div>
  );
};

export default Home;
