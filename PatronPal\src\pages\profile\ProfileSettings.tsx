/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Swal from 'sweetalert2';
import { LogOut } from 'lucide-react';
import {
    updateCustomer,
    selectCurrentCustomer,
    selectCustomerLoading,
    selectCustomerError,
    clearError,
    logout
} from '../../redux-store/slices/customerSlice';
import type { AppDispatch } from '../../redux-store/store';
import OrderHistoryPage from '../OrderHistoryPage';
import Rewards from '../Rewards';

// Tab type definition
type TabType = 'profile' | 'orders' | 'rewards';

const ProfileSettings: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const navigate = useNavigate();
    const currentCustomer = useSelector(selectCurrentCustomer);
    const loading = useSelector(selectCustomerLoading);
    const error = useSelector(selectCustomerError);

    // Add active tab state
    const [activeTab, setActiveTab] = useState<TabType>('profile');

    // Form states - only what we need for the profile
    const [fname, setFname] = useState('');
    const [lname, setLname] = useState('');
    const [mobileNumber, setMobileNumber] = useState('');
    const [email, setEmail] = useState('');
    const [birthDate, setBirthDate] = useState('');
    const [password, setPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [address, setAddress] = useState('');
    const [nameLoading, setNameLoading] = useState(false);
    const [emailLoading, setEmailLoading] = useState(false);
    const [passwordLoading, setPasswordLoading] = useState(false);
    const [paymentLoading, setPaymentLoading] = useState(false);

    // Safe contact getter function
    const getSafeContact = (customer: any): string => {
        console.log('Phone field debugging:', {
            'customer.Phone': customer?.Phone,
            'customer.phone': customer?.phone,
            'customer.contact': customer?.contact,
            'customer.Contact': customer?.Contact
        });
        return customer?.Phone || customer?.phone || customer?.contact || customer?.Contact || '';
    };

    // Safe email getter function
    const getSafeEmail = (customer: any): string => {
        return customer?.Email || customer?.email || '';
    };

    // Safe name getters
    const getSafeFname = (customer: any): string => {
        return customer?.fname || customer?.FirstName || '';
    };

    const getSafeLname = (customer: any): string => {
        return customer?.lname || customer?.LastName || '';
    };

    // Safe address getter function
    const getSafeAddress = (customer: any): string => {
        if (customer?.Address1) {
            return `${customer.Address1.street}, ${customer.Address1.city}, ${customer.Address1.state}, ${customer.Address1.zipcode}`;
        }
        return customer?.address || '';
    };

    // Safe birth date getter function
    const getSafeBirthDate = (customer: any): string => {
        return customer?.birthDate || customer?.birth_date || customer?.dob || '';
    };

    // Initialize form with current customer data
    useEffect(() => {
        if (currentCustomer) {
            console.log('Current customer data:', currentCustomer);

            // Get values using safe getters
            let firstName = getSafeFname(currentCustomer);
            let lastName = getSafeLname(currentCustomer);
            let phoneNumber = getSafeContact(currentCustomer);
            let birthDateValue = getSafeBirthDate(currentCustomer);

            // If Redux state doesn't have the data, try to get it from localStorage directly
            if (!firstName || !lastName || !phoneNumber || !birthDateValue) {
                const storedCustomer = localStorage.getItem('customer');
                if (storedCustomer) {
                    try {
                        const parsedStored = JSON.parse(storedCustomer);
                        console.log('Fallback: Reading from localStorage:', parsedStored);

                        if (!firstName) {
                            firstName = parsedStored.FirstName || parsedStored.fname || '';
                        }
                        if (!lastName) {
                            lastName = parsedStored.LastName || parsedStored.lname || '';
                        }
                        if (!phoneNumber) {
                            phoneNumber = parsedStored.Phone || parsedStored.phone || parsedStored.contact || '';
                        }
                        if (!birthDateValue) {
                            birthDateValue = parsedStored.birthDate || parsedStored.birth_date || '';
                        }
                    } catch (e) {
                        console.error('Error parsing stored customer for fallback:', e);
                    }
                }
            }

            // Use safe getters to handle different field name variations
            setFname(firstName);
            setLname(lastName);
            setEmail(getSafeEmail(currentCustomer));
            setMobileNumber(phoneNumber);
            setBirthDate(birthDateValue);
            setAddress(getSafeAddress(currentCustomer));
        }
    }, [currentCustomer]);

    // Handle errors with improved messaging
    useEffect(() => {
        if (error) {
            console.error('Customer error:', error);
            toast.error(error, {
                position: "top-right",
                autoClose: 5000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
            dispatch(clearError());
        }
    }, [error, dispatch]);

    // Validation functions
    const validateName = (fname: string, lname: string): string | null => {
        if (!fname.trim()) return 'First name is required';
        if (!lname.trim()) return 'Last name is required';
        if (fname.trim().length < 2) return 'First name must be at least 2 characters';
        if (lname.trim().length < 2) return 'Last name must be at least 2 characters';
        return null;
    };

    const validateEmail = (email: string): string | null => {
        if (!email.trim()) return 'Email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) return 'Please enter a valid email address';
        return null;
    };

    const validatePassword = (currentPassword: string, newPassword: string): string | null => {
        if (!currentPassword.trim()) return 'Current password is required';
        if (!newPassword.trim()) return 'New password is required';
        if (newPassword.length < 6) return 'New password must be at least 6 characters long';
        if (currentPassword === newPassword) return 'New password must be different from current password';
        return null;
    };

    const validateMobileNumber = (number: string): string | null => {
        if (number && number.trim()) {
            // Basic mobile number validation (adjust regex as needed for your region)
            const mobileRegex = /^[+]?[\d\s\-\\(\\)]{10,15}$/;
            if (!mobileRegex.test(number.trim())) {
                return 'Please enter a valid mobile number';
            }
        }
        return null;
    };

    const handleSubmit = async (section: string) => {
        if (!currentCustomer?._id) {
            toast.error('User not authenticated. Please log in again.', {
                position: "top-right",
                autoClose: 5000,
            });
            return;
        }

        try {
            switch (section) {
                case 'name': {
                    // Validation
                    const nameError = validateName(fname, lname);
                    if (nameError) {
                        toast.error(nameError, { position: "top-right" });
                        return;
                    }

                    const mobileError = validateMobileNumber(mobileNumber);
                    if (mobileError) {
                        toast.error(mobileError, { position: "top-right" });
                        return;
                    }

                    setNameLoading(true);

                    const nameUpdateData: any = {
                        fname: fname.trim(),
                        lname: lname.trim(),
                        Phone: mobileNumber.trim() || '', // Use Phone instead of contact
                        birthDate: birthDate.trim() || '' // Include birth date
                    };

                    console.log('Updating name with data:', nameUpdateData);

                    await dispatch(updateCustomer({
                        userId: currentCustomer._id,
                        customerData: nameUpdateData
                    })).unwrap();

                    toast.success('Profile updated successfully!', {
                        position: "top-right",
                        autoClose: 3000,
                    });
                    break;
                }

                case 'email': {
                    // Validation
                    const emailError = validateEmail(email);
                    if (emailError) {
                        toast.error(emailError, { position: "top-right" });
                        return;
                    }

                    if (email.trim() === getSafeEmail(currentCustomer)) {
                        toast.info('Email is already up to date', { position: "top-right" });
                        return;
                    }

                    setEmailLoading(true);

                    console.log('Updating email to:', email.trim());

                    await dispatch(updateCustomer({
                        userId: currentCustomer._id,
                        customerData: {
                            email: email.trim()
                        }
                    })).unwrap();

                    toast.success('Email updated successfully!', {
                        position: "top-right",
                        autoClose: 3000,
                    });
                    break;
                }

                case 'password': {
                    // Validation
                    const passwordError = validatePassword(password, newPassword);
                    if (passwordError) {
                        toast.error(passwordError, { position: "top-right" });
                        return;
                    }

                    setPasswordLoading(true);

                    console.log('Updating password');

                    await dispatch(updateCustomer({
                        userId: currentCustomer._id,
                        customerData: {
                            currentPassword: password.trim(),
                            newPassword: newPassword.trim()
                        }
                    })).unwrap();

                    toast.success('Password updated successfully!', {
                        position: "top-right",
                        autoClose: 3000,
                    });

                    // Clear password fields after successful update
                    setPassword('');
                    setNewPassword('');
                    break;
                }

                case 'payment': {
                    if (!address.trim()) {
                        toast.error('Address is required', { position: "top-right" });
                        return;
                    }

                    // Simple parsing: expects address as "street, city, state, zipcode"
                    const addressParts = address.split(',').map(s => s.trim());
                    if (addressParts.length < 4) {
                        toast.error('Please enter address as "street, city, state, zipcode"', {
                            position: "top-right"
                        });
                        return;
                    }

                    const [street, city, state, zipcode] = addressParts;
                    if (!street || !city || !state || !zipcode) {
                        toast.error('All address fields (street, city, state, zipcode) are required', {
                            position: "top-right"
                        });
                        return;
                    }

                    setPaymentLoading(true);

                    console.log('Updating address:', { street, city, state, zipcode });

                    await dispatch(updateCustomer({
                        userId: currentCustomer._id,
                        customerData: {
                            Address1: {
                                street,
                                city,
                                state,
                                zipcode
                            }
                        }
                    })).unwrap();

                    toast.success('Address updated successfully!', {
                        position: "top-right",
                        autoClose: 3000,
                    });
                    break;
                }

                default:
                    toast.error('Unknown section', { position: "top-right" });
                    return;
            }
        } catch (error: any) {
            console.error(`Error updating ${section}:`, error);
            const message = error?.message || error || `Failed to update ${section}`;
            toast.error(message, {
                position: "top-right",
                autoClose: 5000,
            });
        } finally {
            // Reset specific loading states
            switch (section) {
                case 'name':
                    setNameLoading(false);
                    break;
                case 'email':
                    setEmailLoading(false);
                    break;
                case 'password':
                    setPasswordLoading(false);
                    break;
                case 'payment':
                    setPaymentLoading(false);
                    break;
            }
        }
    };

    // Handle logout functionality
    const handleLogout = () => {
        // Show SweetAlert2 logout confirmation
        Swal.fire({
            title: 'Logging out...',
            text: 'You have been successfully logged out',
            icon: 'success',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true,
            width: '500px',
            heightAuto: false,
            padding: '3rem',
            customClass: {
                popup: 'swal2-center-custom',
                title: 'swal2-title-custom'
            },
            backdrop: true,
            allowOutsideClick: false,
            didOpen: (popup) => {
                popup.style.minHeight = '300px';
                popup.style.display = 'flex';
                popup.style.flexDirection = 'column';
                popup.style.justifyContent = 'center';
            }
        });

        // Dispatch logout action which clears all user data
        dispatch(logout());

        // Navigate to home page after logout
        setTimeout(() => {
            navigate('/');
        }, 2000);
    };

    // Show loading state if customer data is not loaded yet
    if (!currentCustomer && loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading profile...</p>
                </div>
            </div>
        );
    }

    if (!currentCustomer && !loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-center">
                    <p className="text-gray-600">Please log in to access profile settings</p>
                </div>
            </div>
        );
    }

    // Render the profile content based on active tab
    const renderContent = () => {
        switch (activeTab) {
            case 'profile':
                return renderProfileSettings();
            case 'orders':
                return <OrderHistoryPage />;
            case 'rewards':
                return <Rewards />;
            default:
                return renderProfileSettings();
        }
    };

    // Profile settings content
    const renderProfileSettings = () => (
        <div className="w-full max-w-md">
            <ToastContainer />
        
            {/* Name Section */}
            <div className="mb-6">
                <h2 className="text-base font-bold mb-6">Update your Profile</h2>
                <div className="mb-4">
                    <label htmlFor="fname" className="block text-sm text-[#828487] mb-2">
                        First Name *
                    </label>
                    <input
                        type="text"
                        id='fname'
                        placeholder="First Name"
                        value={fname}
                        onChange={(e) => setFname(e.target.value)}
                        className="w-full p-3 border border-[#E4E4E4] rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary"
                        disabled={nameLoading}
                    />
                </div>
                <div className="mb-4">
                    <label htmlFor="lname" className="block text-sm text-[#828487] mb-2">
                        Last Name *
                    </label>
                    <input
                        type="text"
                        id='lname'
                        placeholder="Last Name"
                        value={lname}
                        onChange={(e) => setLname(e.target.value)}
                        className="w-full p-3 border border-[#E4E4E4] rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary"
                        disabled={nameLoading}
                    />
                </div>
                <div className="mb-4">
                    <label htmlFor="MobileNumber" className="block text-sm text-[#828487] mb-2">
                        Mobile Number
                    </label>
                    <input
                        type="tel"
                        id='MobileNumber'
                        placeholder="Enter your phone number"
                        value={mobileNumber}
                        onChange={(e) => setMobileNumber(e.target.value)}
                        className="w-full p-3 border border-[#E4E4E4] rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary"
                        disabled={nameLoading}
                    />
                </div>
                <div className="mb-4">
                    <label htmlFor="birthDate" className="block text-sm text-[#828487] mb-2">
                        Birth Date
                    </label>
                    <input
                        type="date"
                        id='birthDate'
                        placeholder="Select your birth date"
                        value={birthDate}
                        onChange={(e) => setBirthDate(e.target.value)}
                        className="w-full p-3 border border-[#E4E4E4] rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary"
                        disabled={nameLoading}
                    />
                </div>
                <button
                    onClick={() => handleSubmit('name')}
                    disabled={nameLoading}
                    className={`w-[50%] py-2 rounded-3xl transition ${nameLoading
                            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                            : 'bg-primary text-white hover:bg-orange-600'
                        }`}
                >
                    {nameLoading ? 'Saving...' : 'Save'}
                </button>
            </div>

            {/* Email Section */}
            <div className="mb-6">
                <h2 className="text-base font-bold mb-6">Email Address</h2>
                <label htmlFor="email" className="block text-sm text-[#828487] mb-2">
                    Email Address *
                </label>
                <input
                    type="email"
                    id='email'
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full mb-4 p-3 border border-[#E4E4E4] rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary"
                    disabled={emailLoading}
                />
                <button
                    onClick={() => handleSubmit('email')}
                    disabled={emailLoading}
                    className={`w-[50%] py-2 rounded-3xl transition ${emailLoading
                            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                            : 'bg-primary text-white hover:bg-orange-600'
                        }`}
                >
                    {emailLoading ? 'Saving...' : 'Save'}
                </button>
            </div>

            {/* Password Section */}
            <div className="mb-6">
                <h2 className="text-base font-bold mb-6">Password</h2>
                <label htmlFor="password" className="block text-sm text-[#828487] mb-2">
                    Current Password *
                </label>
                <input
                    type="password"
                    placeholder="***************"
                    id='password'
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full mb-4 p-3 border border-[#E4E4E4] rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary"
                    disabled={passwordLoading}
                />
                <label htmlFor="NewPassword" className="block text-sm text-[#828487] mb-2">
                    New Password *
                </label>
                <input
                    type="password"
                    placeholder="Set New Password"
                    id="NewPassword"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full p-3 border border-[#E4E4E4] rounded-xl mb-4 focus:outline-none focus:ring-2 focus:ring-primary"
                    disabled={passwordLoading}
                />
                <button
                    onClick={() => handleSubmit('password')}
                    disabled={passwordLoading}
                    className={`w-[50%] py-2 rounded-3xl transition ${passwordLoading
                            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                            : 'bg-primary text-white hover:bg-orange-600'
                        }`}
                >
                    {passwordLoading ? 'Saving...' : 'Save'}
                </button>
            </div>

            {/* Payment Method Section */}
            <div>
                <h2 className="text-base font-bold mb-6">Address</h2>
                <label className="block text-sm text-[#828487] mb-2">
                    Address (Format: street, city, state, zipcode)
                </label>
                <textarea
                    className="w-full h-[162px] p-4 border rounded-xl border-[#E4E4E4] focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder='123 Main Street, New York, NY, 10001'
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    disabled={paymentLoading}
                />
                <button
                    onClick={() => handleSubmit('payment')}
                    disabled={paymentLoading}
                    className={`w-[50%] mt-4 py-2 rounded-3xl transition ${paymentLoading
                            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                            : 'bg-primary text-white hover:bg-orange-600'
                        }`}
                >
                    {paymentLoading ? 'Saving...' : 'Save'}
                </button>
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50 p-5 w-full">
            {/* Header with Logout Button */}
            

            {/* Tabs Navigation */}
            <div className="mb-6 w-full flex justify-between items-center border-b border-gray-200">
                <div className="flex">
                    <button
                        onClick={() => setActiveTab('profile')}
                        className={`px-4 py-2 font-medium text-sm relative ${
                            activeTab === 'profile'
                                ? 'text-primary'
                                : 'text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Profile Settings
                        {activeTab === 'profile' && (
                            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary"></div>
                        )}
                    </button>
                    <button
                        onClick={() => setActiveTab('orders')}
                        className={`px-4 py-2 font-medium text-sm relative ${
                            activeTab === 'orders'
                                ? 'text-primary'
                                : 'text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Order History
                        {activeTab === 'orders' && (
                            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary"></div>
                        )}
                    </button>
                    <button
                        onClick={() => setActiveTab('rewards')}
                        className={`px-4 py-2 font-medium text-sm relative ${
                            activeTab === 'rewards'
                                ? 'text-primary'
                                : 'text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Rewards
                        {activeTab === 'rewards' && (
                            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary"></div>
                        )}
                    </button>
                </div>
                <button
                    onClick={handleLogout}
                    className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 font-medium"
                >
                    <LogOut size={18} />
                    Logout
                </button>
            </div>

            {/* Content Area */}
            <div className="flex justify-center w-full">
                {renderContent()}
            </div>
        </div>
    );
};

export default ProfileSettings;
