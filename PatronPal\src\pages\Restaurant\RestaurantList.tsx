/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Heart, Star, MapPin, Clock, PhoneIcon } from 'lucide-react';
import {
  getDevicesPatronpal,
  addFavorite,
  selectDevices,
  selectDeviceLoading,
  selectDeviceError,
  selectFavoriteLoading
} from '../../redux-store/slices/deviceSlice';
import { assets } from '../../assets/assets';
import { handleRestaurantTaxData } from '../../utils/taxUtils';
import Breadcrumb from '../../components/Breadcrumb';

// Enhanced Restaurant interface that maps to your Device structure
interface Restaurant {
  _id: string;
  name: string;
  businessType?: string;
  image?: string;
  Line1: string;
  Line2?: string;
  City: string;
  State: string;
  Phoneno: string;
  PostalCode: string;
  Country: string;
  active: boolean;
  userId: any;
  delivery?: boolean | string;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  Streetaddress?: string;
  reviews?: Array<{
    _id?: string;
    food: number;
    service: number;
    ambiance: number;
    testimonial: string;
    customerId: any;
    averageScore?: number;
  }>;
  favorites?: Array<{
    _id?: string;
    customerId: any;
  }>;
}


// Redux state type
interface RootState {
  customer: any;
  device: {
    devices: Restaurant[];
    loading: boolean;
    error: string | null;
    favoriteLoading: boolean;
  };
  auth?: {
    user?: {
      _id: string;
    };
  };
}

// Utility functions for data transformation
const calculateAverageRating = (reviews: Restaurant['reviews']): number | null => {
  if (!reviews || reviews.length === 0) return null;

  const totalReviews = reviews.length;
  const averageFood = reviews.reduce((sum, review) => sum + review.food, 0) / totalReviews;
  const averageService = reviews.reduce((sum, review) => sum + review.service, 0) / totalReviews;
  const averageAmbiance = reviews.reduce((sum, review) => sum + review.ambiance, 0) / totalReviews;

  const overallAverage = (averageFood + averageService + averageAmbiance) / 3;
  return Number(overallAverage.toFixed(1));
};

const formatReviewCount = (reviews: Restaurant['reviews']): string | null => {
  if (!reviews || reviews.length === 0) return null;
  return reviews.length > 100 ? '100+' : `${reviews.length}+`;
};

const isRestaurantFavorited = (restaurant: Restaurant, userId: string): boolean => {
  if (!restaurant.favorites || restaurant.favorites.length === 0) return false;

  return restaurant.favorites.some(fav => {
    if (typeof fav.customerId === 'object' && fav.customerId?._id) {
      return fav.customerId._id === userId;
    }
    return fav.customerId === userId;
  });
};

const generatePromoText = (restaurant: Restaurant): string => {
  if (restaurant.ChargesFreeKm && restaurant.ChargesFreeKm > 0) {
    return `Free delivery up to ${restaurant.ChargesFreeKm}km`;
  }
  if (restaurant.delivery) {
    return 'Delivery available';
  }
  return 'Special offers available';
};

const generateDiscountInfo = (restaurant: Restaurant): { discount: string; code: string } => {
  if (restaurant.ChargesperKm && restaurant.ChargesperKm < 3) {
    return { discount: '$5 off:', code: 'SAVE5' };
  }
  return { discount: '10% off:', code: 'DEAL10' };
};

// Utility function to check if delivery is available
const isDeliveryAvailable = (restaurant: Restaurant): boolean => {
  return restaurant.delivery === true || restaurant.delivery === "true";
};


// Utility function to check if pickup is available
const isPickupAvailable = (restaurant: Restaurant): boolean => {
  return restaurant.delivery === false || restaurant.delivery === "false";
};

const FoodDeliveryHeader: React.FC<{ serviceType: string }> = () => {


  return (
    <div className="bg-[#F97918] w-full overflow-hidden rounded-xl">
      <div className="container-fluid mx-auto pl-4 py-6 pr-0 flex items-center h-[250px] justify-between">
        <div className="text-white">
          <h5 className="text-2xl font-semibold md:px-16">
            Order Great Food, Anywhere, Anytime
          </h5>
        </div>
        <div className="relative hidden md:block w-[700px] h-[650px]">
          <img
            src={assets.banner3}
            alt="Food Image"
            className="h-full w-full object-contain object-bottom"
          />
        </div>
      </div>
    </div>
  );
};
// resturentList RestaurantCard
const RestaurantCard: React.FC<{
  restaurant: Restaurant;
  userId: string;
  onFavoriteToggle: (restaurantId: string) => void;
  favoriteLoading: boolean;
  serviceType: string;
  onRestaurantClick: (restaurantId: string) => void;
}> = ({ restaurant, userId, onFavoriteToggle, serviceType, onRestaurantClick }) => {
  const rating = calculateAverageRating(restaurant.reviews);
  const reviewCount = formatReviewCount(restaurant.reviews);
  const isFavorited = isRestaurantFavorited(restaurant, userId);
  const promoText = generatePromoText(restaurant);
  const { discount, code } = generateDiscountInfo(restaurant);

  const isOpen = () => {
    if (serviceType.toLowerCase() === 'delivery') {
      if (!restaurant.deliveryStartTime || !restaurant.deliveryEndTime) return true;
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(restaurant.deliveryStartTime.replace(':', ''));
      const endTime = parseInt(restaurant.deliveryEndTime.replace(':', ''));
      return currentTime >= startTime && currentTime <= endTime;
    } else if (serviceType.toLowerCase() === 'pickup') {
      if (!restaurant.pickupStartTime || !restaurant.pickupEndTime) return true;
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(restaurant.pickupStartTime.replace(':', ''));
      const endTime = parseInt(restaurant.pickupEndTime.replace(':', ''));
      return currentTime >= startTime && currentTime <= endTime;
    }
    return true;
  };

  const formatAddress = () => {
    const parts = [restaurant.Line1, restaurant.Line2, restaurant.City];
    const filteredParts = parts.filter(
      part =>
        part &&
        part.trim().toLowerCase() !== 'undefined' &&
        part.trim() !== ''
    );
    return filteredParts.join(', ');
  };

  function isValidTime(time?: string | null): boolean {
    return (
      typeof time === 'string' &&
      time.trim() !== '' &&
      time.trim().toLowerCase() !== 'undefined' &&
      time.trim().toLowerCase() !== 'null'
    );
  }

  // ✅ 24-hour → 12-hour formatter
  function formatTime12Hour(timeStr: string): string {
    const [hours, minutes] = timeStr.split(':');
    const date = new Date();
    date.setHours(parseInt(hours));
    date.setMinutes(parseInt(minutes));

    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  }

  const getServiceTimes = () => {
    if (serviceType.toLowerCase() === 'delivery') {
      return {
        startTime: restaurant.deliveryStartTime,
        endTime: restaurant.deliveryEndTime
      };
    } else if (serviceType.toLowerCase() === 'pickup') {
      return {
        startTime: restaurant.pickupStartTime,
        endTime: restaurant.pickupEndTime
      };
    }
    return {
      startTime: restaurant.deliveryStartTime,
      endTime: restaurant.deliveryEndTime
    };
  };

  const { startTime, endTime } = getServiceTimes();

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on the favorite button
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    onRestaurantClick(restaurant._id);
  };

  return (
    <div
      className="relative rounded-lg overflow-hidden bg-white p-1 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleCardClick}
    >
      <div className="relative">
        <img
          src={restaurant.image || 'https://patronpal.com/assets/aaa.jpeg'}
          alt={restaurant.name}
          className="w-full h-40 object-cover rounded-lg"
        />
        <div className="absolute inset-0 bg-black/20 bg-opacity-50 rounded-lg"></div>

        {/* Promo Text */}
        <div className="absolute top-4 left-4 bg-orange-500 text-white text-xs px-2 py-1 rounded-xl">
          {promoText}
        </div>

        {/* Discount Info */}
        <div className="absolute top-12 left-4 bg-orange-500 text-white text-xs px-2 py-1 rounded-xl">
          <span className="font-bold">{discount}</span> {code}
        </div>

        {/* Favorite Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onFavoriteToggle(restaurant._id);
          }}
          className="absolute top-4 right-4 bg-white p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
        >
          <Heart
            className={`h-5 w-5 transition-all duration-200 ${
              isFavorited
                ? 'text-orange-500 fill-current scale-110'
                : 'text-gray-400 hover:text-orange-500'
            }`}
          />
        </button>

        {/* Status Indicator */}
        {!isOpen() && (
          <div className="absolute bottom-4 left-4 bg-red-500 text-white text-xs px-2 py-1 rounded-xl">
            Closed
          </div>
        )}

        {/* Active Status */}
        {!restaurant.active && (
          <div className="absolute bottom-4 right-4 bg-gray-500 text-white text-xs px-2 py-1 rounded-xl">
            Inactive
          </div>
        )}
      </div>

      <div className="pt-2 pb-0 p-1">
        <div className="flex items-start justify-between space-x-4">
          {/* LEFT: Restaurant Details */}
          <div className="flex-1 min-w-0">
            <h3 className="text-md font-semibold text-gray-900 truncate max-w-full sm:max-w-[200px] overflow-hidden whitespace-nowrap">
              {restaurant.name}
            </h3>

            <p className="text-gray-600 text-sm">
              {restaurant.businessType || 'Restaurant'}
            </p>
            <div className='flex flex-col space-y-0.5 py-1 items-start'>
              {/* Location */}
              <div className="flex items-center mt-1 space-x-1 text-gray-500">
                <MapPin className="h-4 w-4 shrink-0 flex-none" />
                <span className="truncate max-w-full text-xs sm:max-w-[200px] overflow-hidden whitespace-nowrap">
                  {formatAddress()}
                </span>
              </div>

              {/* Phone */}
              {restaurant.Phoneno && restaurant.Phoneno !== 'null' && restaurant.Phoneno !== 'undefined' && (
                <div className="flex items-center mt-1 text-xs text-gray-500">
                  <PhoneIcon className="h-3 w-3 mr-1" />
                  <span>
                    {restaurant.Phoneno}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* RIGHT: Rating */}
          {rating !== null && reviewCount && (
            <div className="flex items-center space-x-1">
              <Star className="h-4 w-4 text-yellow-400 fill-current" />
              <span className="text-sm font-medium">{rating}</span>
              <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-xl">
                {reviewCount}
              </span>
            </div>
          )}
        </div>

        <div className='flex justify-between items-center w-full'>
          {/* Service Time Info */}
          {isValidTime(startTime) && isValidTime(endTime) && (
            <div className="flex items-center mt-1 text-xs text-gray-500">
              <Clock className="h-4 w-4 mr-1 shrink-0 flex-none" />
              <span>
                {formatTime12Hour(startTime!)} - {formatTime12Hour(endTime!)}
              </span>
            </div>
          )}

          {/* Delivery Charges - only show for delivery */}
          {serviceType.toLowerCase() === 'delivery' && restaurant.ChargesperKm !== 0 && restaurant.ChargesperKm && (
            <div className="flex items-center mt-1 text-xs text-gray-500">
              <span>${restaurant.ChargesperKm}/km delivery</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};


const RestaurantList: React.FC = () => {
  const dispatch = useDispatch();
  const restaurants = useSelector(selectDevices);
  const loading = useSelector(selectDeviceLoading);
  const error = useSelector(selectDeviceError);
  const favoriteLoading = useSelector(selectFavoriteLoading);
  // const query = useQuery();

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  // Removed NavigateOption as it is not used and contains a syntax error


  // Get service type from URL or default to 'all'
  const [serviceType, setServiceType] = useState<string>('all');

  // State to store the state from URL
  const [stateFilter, setStateFilter] = useState<string | null>(null);

  // Get user ID from auth state
  const userId = useSelector((state: RootState) => state.customer?.currentCustomer?._id);

  // Update serviceType and stateFilter based on URL parameters
  useEffect(() => {

    console.log('userId', userId)
    const typeParam = searchParams.get('type');
    if (typeParam === 'delivery' || typeParam === 'pickup') {
      setServiceType(typeParam);
    } else {
      setServiceType('all');
    }

    const stateParam = searchParams.get('state');
    setStateFilter(stateParam);
  }, [searchParams]);

  // Fetch all restaurants
  useEffect(() => {
    dispatch(getDevicesPatronpal({}) as any);
  }, [dispatch]);

  // State matching function (same as in useRestaurantData)
  const isStateMatch = (deviceState: string, filterState: string): boolean => {
    if (!deviceState || !filterState) return false;

    const deviceStateLower = deviceState.toLowerCase().trim();
    const filterStateLower = filterState.toLowerCase().trim();

    // Direct match
    if (deviceStateLower === filterStateLower) return true;

    // Check common state abbreviations
    const stateMapping: { [key: string]: string[] } = {
      "georgia": ["ga", "georgia"],
      "california": ["ca", "california"],
      "texas": ["tx", "texas"],
      "florida": ["fl", "florida"],
      "new york": ["ny", "new york"],
      // Add more as needed
    };

    for (const [_, variations] of Object.entries(stateMapping)) {
      const variationsLower = variations.map(v => v.toLowerCase());
      if (variationsLower.includes(filterStateLower) && variationsLower.includes(deviceStateLower)) {
        return true;
      }
    }

    return false;
  };

  // Filter restaurants based on service type, active status, and state
  const { filteredRestaurants } = useMemo(() => {
    // First filter by active status
    const activeRestaurants = restaurants.filter(restaurant => restaurant.active);

    // Then filter by state if provided
    const stateFilteredRestaurants = stateFilter
      ? activeRestaurants.filter(restaurant => isStateMatch(restaurant.State, stateFilter))
      : activeRestaurants;

    // Then filter by service type
    const deliveryRestaurants = stateFilteredRestaurants.filter(isDeliveryAvailable);
    const pickupRestaurants = stateFilteredRestaurants.filter(isPickupAvailable);

    let filtered: Restaurant[] = [];

    switch (serviceType.toLowerCase()) {
      case 'delivery':
        filtered = deliveryRestaurants;
        break;
      case 'pickup':
        filtered = pickupRestaurants;
        break;
      default:
        filtered = stateFilteredRestaurants.filter(
          restaurant => isDeliveryAvailable(restaurant) || isPickupAvailable(restaurant)
        );
    }

    return {
      filteredRestaurants: filtered,
      deliveryCount: deliveryRestaurants.length,
      pickupCount: pickupRestaurants.length
    };
  }, [restaurants, serviceType, stateFilter]);

  const handleFavoriteToggle = (restaurantId: string) => {
    if (!userId) {
      navigate('/login'); // client-side navigation
      return; // prevent further execution
    }

    dispatch(addFavorite({
      deviceId: restaurantId,
      customerId: userId
    }) as any);
  };

  const handleRestaurantClick = async (restaurantId: string, userId: string) => {
    // Store userId and fetch tax data using utility function
    try {
      await handleRestaurantTaxData(userId);
    } catch (error) {
      console.error('Error handling restaurant tax data:', error);
    }

    // Navigate to the product page
    navigate(`/product/${restaurantId}/${userId}`);
  };

  const handleRetry = () => {
    dispatch(getDevicesPatronpal({}) as any);
  };


  if (loading) {
    return (
      <div className="bg-gray-50 min-h-screen">
        <FoodDeliveryHeader serviceType={serviceType} />
        <div className="container mx-auto md:px-16 py-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading restaurants...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-50 min-h-screen">
        <FoodDeliveryHeader serviceType={serviceType} />
        <div className="container mx-auto md:px-16 py-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center max-w-md mx-auto">
              <div className="mb-6">
                <svg className="w-16 h-16 mx-auto text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">No Restaurants Available</h2>
              <p className="text-gray-600 mb-6">
                We couldn't find any restaurants at the moment. This could be due to maintenance or network issues.
              </p>
              <button
                onClick={handleRetry}
                className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 cursor-pointer font-medium flex items-center justify-center mx-auto"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Update breadcrumb to include state information if available
  const breadcrumbItems = [
    { label: "Home", path: "/" },
    { label: stateFilter ? `${stateFilter}` : 'All Restaurants', path: `?type=${serviceType}${stateFilter ? `&state=${encodeURIComponent(stateFilter)}` : ''}` },
  ];



  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <FoodDeliveryHeader serviceType={serviceType} />

      {/* Navigation */}
      <div className="container mx-auto md:px-12">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Main Content */}
      <div className="container mx-auto md:px-16 px-4 py-6 ">

        <div className="flex justify-between items-center mb-6">
          <h2 className="md:text-2xl text-lg font-bold">
            {stateFilter ? `Restaurants in ${stateFilter}` : 'All Restaurants'}
          </h2>
          <span className="text-gray-600">
            {filteredRestaurants.length} restaurant{filteredRestaurants.length !== 1 ? 's' : ''} found
          </span>
        </div>

        {/* Restaurant Grid */}

        {filteredRestaurants.length > 0 ? (

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ">

            {filteredRestaurants.map((restaurant: Restaurant) => (

              <RestaurantCard
                key={restaurant._id}
                restaurant={restaurant}
                userId={userId}
                onFavoriteToggle={handleFavoriteToggle}
                favoriteLoading={favoriteLoading}
                serviceType={serviceType}
                onRestaurantClick={() => handleRestaurantClick(restaurant._id, restaurant.userId?._id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No {serviceType === 'all' ? '' : serviceType} restaurants found
            </h3>
            <p className="text-gray-600">
              {serviceType === 'delivery' ? 'No restaurants with delivery service are available at the moment' :
                serviceType === 'pickup' ? 'No restaurants with pickup service are available at the moment' :
                  'No active restaurants available at the moment'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RestaurantList;
