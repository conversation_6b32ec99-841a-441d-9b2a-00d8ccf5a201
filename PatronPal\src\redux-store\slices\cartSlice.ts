/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../store";
import { logout } from "./customerSlice"; // Import the logout action

// Types
export interface CartItem {
  id: string;
  _id: string;
  name: string;
  cuisine?: string;
  parentCategory?: string;
  rating?: number;
  image?: string;
  deal?: string;
  pizzaOffer?: string;
  priceOff?: string;
  price: number;
  discountPrice?: number;
  active?: boolean;
  userId?: string;
  quantity: number;
  totalQuantity?: number;
  isFree?: boolean;
  modifiers?: any;
  modifierPrice?: number;
  note?: string;
  restaurantId?: string;
  restaurant?: {
    id: string;
    name: string;
    image?: string;
    businessType?: string;
    deliveryInfo?: {
      chargesPerKm: number;
      freeDistanceMeters: number;
      baseDeliveryCharge?: number;
    };
    // Support for the actual field names from your API
    ChargesperKm?: number;
    ChargesFreeKm?: number; // This is actually free distance in meters
  };
}

interface RestaurantDeliveryCharges {
  [restaurantId: string]: {
    restaurantName: string;
    deliveryCharge: number;
    distance?: number;
    deliveryInfo?: {
      chargesPerKm: number;
      freeDistanceMeters: number;
      baseDeliveryCharge?: number;
    };
  };
}

interface CartState {
  items: CartItem[];
  totalPrice: number;
  subtotal: number;
  taxAmount: number;
  taxPercentage: number;
  deliveryCharges: number;
  deliveryDistanceMeters?: number; // Store distance in meters for consistency
  restaurantDeliveryInfo?: {
    chargesPerKm: number;
    freeDistanceMeters: number;
  };
  restaurantDeliveryCharges: RestaurantDeliveryCharges;
  totalDeliveryCharges: number;
}

const initialState: CartState = {
  items: [],
  totalPrice: 0,
  subtotal: 0,
  taxAmount: 0,
  taxPercentage: 0,
  deliveryCharges: 0,
  deliveryDistanceMeters: 0,
  restaurantDeliveryInfo: undefined,
  restaurantDeliveryCharges: {},
  totalDeliveryCharges: 0,
};

const calculateDeliveryCharges = (
  distanceMeters: number,
  chargesPerKm: number,
  freeDistanceMeters: number,
  baseDeliveryCharge: number = 0
): number => {
  // If distance is within free delivery range, only charge base delivery charge
  if (distanceMeters <= freeDistanceMeters) {
    return baseDeliveryCharge;
  }
  
  // Calculate chargeable distance in kilometers
  const chargeableDistanceMeters = distanceMeters - freeDistanceMeters;
  const chargeableDistanceKm = chargeableDistanceMeters / 1000;
  
  // Calculate total charges: base + (chargeable distance in km * rate per km)
  return baseDeliveryCharge + (chargeableDistanceKm * chargesPerKm);
};

const calculateTotal = (items: CartItem[]): number => {
  return items.reduce((total, item) => {
    const baseItemPrice = item.discountPrice || item.price;
    let modifierPrice = 0;

    // Calculate total modifier price with null/undefined checks
    if (item.modifiers) {
      Object.values(item.modifiers).forEach((modifier: any) => {
        // Check if modifier and modifier.price exist before adding
        if (modifier && typeof modifier.price === "number") {
          modifierPrice += modifier.price;
        }
      });
    }

    // Total price for this item = (base price + modifier price) * quantity
    const itemTotalPrice = (baseItemPrice + modifierPrice) * item.quantity;
    return total + itemTotalPrice;
  }, 0);
};

const calculateTotalWithTax = (items: CartItem[]): { subtotal: number; taxAmount: number; total: number; taxPercentage: number } => {
  const subtotal = calculateTotal(items);

  // Get tax percentage from localStorage (set when restaurant is selected)
  const taxPercentage = parseFloat(localStorage.getItem('totalTaxPercentage') || '0');
  const taxAmount = (subtotal * taxPercentage) / 100;
  const total = subtotal + taxAmount;

  return {
    subtotal: parseFloat(subtotal.toFixed(2)),
    taxAmount: parseFloat(taxAmount.toFixed(2)),
    total: parseFloat(total.toFixed(2)),
    taxPercentage: taxPercentage
  };
};

// Helper function to update all price calculations including tax
const updateCartTotals = (state: CartState) => {
  try {
    const calculations = calculateTotalWithTax(state.items);
    state.subtotal = calculations.subtotal;
    state.taxAmount = calculations.taxAmount;
    state.totalPrice = calculations.total;
    state.taxPercentage = calculations.taxPercentage;
  } catch (error) {
    console.error('Error updating cart totals:', error);
    // Fallback to basic calculation without tax
    state.subtotal = calculateTotal(state.items);
    state.taxAmount = 0;
    state.totalPrice = state.subtotal;
    state.taxPercentage = 0;
  }
};

const calculateTotalDeliveryCharges = (restaurantDeliveryCharges: RestaurantDeliveryCharges): number => {
  return Object.values(restaurantDeliveryCharges).reduce((total, restaurant) => {
    return total + restaurant.deliveryCharge;
  }, 0);
};

// Helper function to get delivery info from restaurant data
const getDeliveryInfo = (restaurant: any) => {
  // Check if deliveryInfo exists (normalized format)
  if (restaurant?.deliveryInfo) {
    return restaurant.deliveryInfo;
  }
  
  // Check if the API format exists (ChargesperKm, ChargesFreeKm)
  if (restaurant?.ChargesperKm !== undefined && restaurant?.ChargesFreeKm !== undefined) {
    return {
      chargesPerKm: restaurant.ChargesperKm,
      freeDistanceMeters: restaurant.ChargesFreeKm, // This is in meters
      baseDeliveryCharge: 0, // Default base charge
    };
  }
  
  return null;
};

const updateRestaurantDeliveryCharges = (
  state: CartState,
  deliveryDistanceMeters?: number
): void => {
  const restaurantGroups: { [key: string]: CartItem[] } = {};
  
  // Group items by restaurant
  state.items.forEach(item => {
    const restaurantId = item.restaurant?.id || item.restaurantId || 'unknown';
    if (!restaurantGroups[restaurantId]) {
      restaurantGroups[restaurantId] = [];
    }
    restaurantGroups[restaurantId].push(item);
  });

  // Calculate delivery charges for each restaurant
  const newRestaurantDeliveryCharges: RestaurantDeliveryCharges = {};
  
  Object.entries(restaurantGroups).forEach(([restaurantId, items]) => {
    const firstItem = items[0];
    const restaurant = firstItem.restaurant;
    const restaurantName = restaurant?.name || 'Unknown Restaurant';
    
    let deliveryCharge = 0;
    
    // Get delivery info using the helper function
    const deliveryInfo = getDeliveryInfo(restaurant);
    
    if (deliveryInfo) {
      const distanceMeters = deliveryDistanceMeters || state.deliveryDistanceMeters || 0;
      deliveryCharge = calculateDeliveryCharges(
        distanceMeters,
        deliveryInfo.chargesPerKm,
        deliveryInfo.freeDistanceMeters,
        deliveryInfo.baseDeliveryCharge || 0
      );
    }
    
    newRestaurantDeliveryCharges[restaurantId] = {
      restaurantName,
      deliveryCharge,
      distance: deliveryDistanceMeters || state.deliveryDistanceMeters,
      deliveryInfo,
    };
  });

  state.restaurantDeliveryCharges = newRestaurantDeliveryCharges;
  state.totalDeliveryCharges = calculateTotalDeliveryCharges(newRestaurantDeliveryCharges);
};

export const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addItem: (state, action: PayloadAction<CartItem>) => {
      const newItem = action.payload;

      // Calculate modifier price total - Fix the null/undefined error
      let modifierPrice = 0;
      if (newItem.modifiers) {
        Object.values(newItem.modifiers).forEach((modifier: any) => {
          // Check if modifier and modifier.price exist before adding
          if (modifier && typeof modifier.price === "number") {
            modifierPrice += modifier.price;
          }
        });
      }
      newItem.modifierPrice = modifierPrice;

      // Check if the exact same item with same modifiers exists
      const existingIndex = state.items.findIndex((item) => {
        if (item._id !== newItem._id) return false;

        // Compare modifiers - Add null/undefined checks
        const itemModKeys = Object.keys(item.modifiers || {});
        const newModKeys = Object.keys(newItem.modifiers || {});

        // If different number of modifiers, not the same
        if (itemModKeys.length !== newModKeys.length) return false;

        // Check if all modifiers match - Add null/undefined checks
        for (const key of itemModKeys) {
          if (
            !newItem.modifiers ||
            !newItem.modifiers[key] ||
            !item.modifiers[key] ||
            item.modifiers[key].name !== newItem.modifiers[key].name
          ) {
            return false;
          }
        }

        // Check if notes match
        if (item.note !== newItem.note) return false;

        return true;
      });

      if (existingIndex >= 0) {
        // Item exists with same modifiers, increase quantity
        state.items[existingIndex].quantity += 1;
      } else {
        // Add new item
        state.items.push(newItem);
      }

      // Recalculate total price with tax and delivery charges
      updateCartTotals(state);
      updateRestaurantDeliveryCharges(state);
    },

    updateItemQuantity: (
      state,
      action: PayloadAction<{ id: string; action: "increase" | "decrease" }>
    ) => {
      const { id, action: quantityAction } = action.payload;

      state.items = state.items
        .map((item) => {
          if (item._id === id) {
            return {
              ...item,
              quantity:
                quantityAction === "increase"
                  ? item.quantity + 1
                  : item.quantity - 1,
            };
          }
          return item;
        })
        .filter((item) => item.quantity > 0); // Remove items with quantity 0

      // Recalculate total price with tax and delivery charges
      updateCartTotals(state);
      updateRestaurantDeliveryCharges(state);
    },

    updateItemModifiers: (
      state,
      action: PayloadAction<{ id: string; modifiers: any; note?: string }>
    ) => {
      const { id, modifiers, note } = action.payload;

      state.items = state.items.map((item) => {
        if (item._id === id) {
          // Calculate modifier price total
          let modifierPrice = 0;
          if (modifiers) {
            Object.values(modifiers).forEach((modifier: any) => {
              modifierPrice += modifier.price || 0;
            });
          }

          return {
            ...item,
            modifiers,
            note,
            modifierPrice,
          };
        }
        return item;
      });

      // Recalculate total price with tax
      updateCartTotals(state);
    },

    setDeliveryInfo: (
      state,
      action: PayloadAction<{
        distanceMeters: number;
        chargesPerKm: number;
        freeDistanceMeters: number;
      }>
    ) => {
      const { distanceMeters, chargesPerKm, freeDistanceMeters } = action.payload;
      state.deliveryDistanceMeters = distanceMeters;
      state.restaurantDeliveryInfo = { chargesPerKm, freeDistanceMeters };
      state.deliveryCharges = calculateDeliveryCharges(
        distanceMeters,
        chargesPerKm,
        freeDistanceMeters
      );
      
      // Update restaurant-specific delivery charges
      updateRestaurantDeliveryCharges(state, distanceMeters);
    },

    setRestaurantDeliveryInfo: (
      state,
      action: PayloadAction<{
        restaurantId: string;
        deliveryInfo: {
          chargesPerKm: number;
          freeDistanceMeters: number;
          baseDeliveryCharge?: number;
        };
        distanceMeters?: number;
      }>
    ) => {
      const { restaurantId, deliveryInfo, distanceMeters } = action.payload;
      
      // Update the restaurant's delivery info in existing items
      state.items = state.items.map(item => {
        if ((item.restaurant?.id || item.restaurantId) === restaurantId) {
          return {
            ...item,
            restaurant: {
              ...(item.restaurant ?? {
                id: item.restaurantId ?? restaurantId,
                name: "",
              }),
              id: (item.restaurant?.id ?? item.restaurantId ?? restaurantId) as string,
              deliveryInfo,
            },
          };
        }
        return item;
      });

      // Recalculate delivery charges
      updateRestaurantDeliveryCharges(state, distanceMeters);
    },

    updateDeliveryDistance: (state, action: PayloadAction<number>) => {
      const distanceMeters = action.payload;
      state.deliveryDistanceMeters = distanceMeters;

      if (state.restaurantDeliveryInfo) {
        state.deliveryCharges = calculateDeliveryCharges(
          distanceMeters,
          state.restaurantDeliveryInfo.chargesPerKm,
          state.restaurantDeliveryInfo.freeDistanceMeters
        );
      }

      // Update restaurant-specific delivery charges
      updateRestaurantDeliveryCharges(state, distanceMeters);
    },

    removeItem: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter((item) => item._id !== action.payload);
      updateCartTotals(state);
      updateRestaurantDeliveryCharges(state);
    },

    clearCart: (state) => {
      state.items = [];
      state.totalPrice = 0;
      state.subtotal = 0;
      state.taxAmount = 0;
      state.taxPercentage = 0;
      state.deliveryCharges = 0;
      state.deliveryDistanceMeters = 0;
      state.restaurantDeliveryInfo = undefined;
      state.restaurantDeliveryCharges = {};
      state.totalDeliveryCharges = 0;
    },

    // Action to recalculate taxes when restaurant changes
    recalculateTaxes: (state) => {
      updateCartTotals(state);
    },
  },
  extraReducers: (builder) => {
    builder.addCase(logout, (state) => {
      // Clear cart on logout
      state.items = [];
      state.totalPrice = 0;
      state.subtotal = 0;
      state.taxAmount = 0;
      state.taxPercentage = 0;
      state.deliveryCharges = 0;
      state.deliveryDistanceMeters = 0;
      state.restaurantDeliveryInfo = undefined;
      state.restaurantDeliveryCharges = {};
      state.totalDeliveryCharges = 0;
    });
  },
});

// Export actions
export const {
  addItem,
  updateItemQuantity,
  updateItemModifiers,
  removeItem,
  clearCart,
  setDeliveryInfo,
  updateDeliveryDistance,
  setRestaurantDeliveryInfo,
  recalculateTaxes,
} = cartSlice.actions;

// Selectors
export const selectCartItems = (state: RootState) => state.cart.items;
export const selectCartTotal = (state: RootState) => state.cart.totalPrice;
export const selectCartSubtotal = (state: RootState) => state.cart.subtotal;
export const selectCartTaxAmount = (state: RootState) => state.cart.taxAmount;
export const selectCartTaxPercentage = (state: RootState) => state.cart.taxPercentage;
export const selectCartItemCount = (state: RootState) =>
  state.cart.items.reduce((count, item) => count + item.quantity, 0);
export const selectDeliveryCharges = (state: RootState) =>
  state.cart.deliveryCharges;
export const selectDeliveryDistanceMeters = (state: RootState) =>
  state.cart.deliveryDistanceMeters;
export const selectDeliveryDistanceKm = (state: RootState) =>
  (state.cart.deliveryDistanceMeters || 0) / 1000;
export const selectTotalWithDelivery = (state: RootState) =>
  state.cart.totalPrice + state.cart.totalDeliveryCharges;
export const selectRestaurantDeliveryCharges = (state: RootState) =>
  state.cart.restaurantDeliveryCharges;
export const selectTotalDeliveryCharges = (state: RootState) =>
  state.cart.totalDeliveryCharges;

export default cartSlice.reducer;