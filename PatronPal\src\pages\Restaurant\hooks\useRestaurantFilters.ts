import { useState, useMemo } from 'react';
import type { Restaurant } from '../types/types';

export const useRestaurantFilters = (restaurants: Restaurant[]) => {
  const [activeFilters, setActiveFilters] = useState({
    sort: 'relevance',
    offers: [] as string[],
    businessTypes: [] as string[],
    cuisines: [] as string[], // Add this line
  });

  const filteredRestaurants = useMemo(() => {
    let filtered = [...restaurants];

    // Filter by business types
    if (activeFilters.businessTypes.length > 0) {
      filtered = filtered.filter(r => 
        activeFilters.businessTypes.some(type =>
          r.businessType?.toLowerCase() === type.toLowerCase()
        )
      );
    }

    // Filter by cuisines - Add this block
    if (activeFilters.cuisines.length > 0) {
      filtered = filtered.filter(r => 
        activeFilters.cuisines.some(cuisine =>
          r.businessType?.toLowerCase().includes(cuisine.toLowerCase()) ||
          r.name?.toLowerCase().includes(cuisine.toLowerCase())
        )
      );
    }

    // Filter by offers
    if (activeFilters.offers.length > 0) {
      filtered = filtered.filter(r => {
        return activeFilters.offers.some(offer => {
          switch (offer) {
            case 'delivery':
              return r.delivery === true || r.delivery === "true";
            case 'vouchers':
              return r.ChargesFreeKm && r.ChargesFreeKm > 0;
            case 'deals':
              return r.ChargesperKm && r.ChargesperKm < 2;
            case 'ratings4+':
              const avgRating = calculateAverageRating(r.reviews);
              return avgRating && avgRating >= 4;
            case 'toprestaurants':
              const topRating = calculateAverageRating(r.reviews);
              return topRating && topRating >= 4.5;
            default:
              return false;
          }
        });
      });
    }

    // Sort
    switch (activeFilters.sort) {
      case 'featured':
        filtered.sort((a, b) => {
          const ratingA = calculateAverageRating(a.reviews) || 0;
          const ratingB = calculateAverageRating(b.reviews) || 0;
          return ratingB - ratingA;
        });
        break;
      case 'bestselling':
        filtered.sort((a, b) => (b.reviews?.length || 0) - (a.reviews?.length || 0));
        break;
    }

    return filtered;
  }, [restaurants, activeFilters]);

  const calculateAverageRating = (reviews: Restaurant['reviews']): number | null => {
    if (!reviews || reviews.length === 0) return null;
    const totalReviews = reviews.length;
    const averageFood = reviews.reduce((sum, review) => sum + review.food, 0) / totalReviews;
    const averageService = reviews.reduce((sum, review) => sum + review.service, 0) / totalReviews;
    const averageAmbiance = reviews.reduce((sum, review) => sum + review.ambiance, 0) / totalReviews;
    return Number(((averageFood + averageService + averageAmbiance) / 3).toFixed(1));
  };

  return {
    activeFilters,
    setActiveFilters,
    filteredRestaurants,
    calculateAverageRating
  };
};