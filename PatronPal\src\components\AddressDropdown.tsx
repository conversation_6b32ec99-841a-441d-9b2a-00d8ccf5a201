/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from 'react';
import { IoLocationOutline } from "react-icons/io5";

interface AddressData {
  Line1: string;
  Line2: string;
  City: string;
  State: string;
  PostalCode: string;
  Country: string;
}

interface AddressDropdownProps {
  value: string;
  onSelectAddress: (address: AddressData) => void;
  onInputChange?: (value: string) => void;
  autoFocus?: boolean;
}

const AddressDropdown: React.FC<AddressDropdownProps> = ({
  value,
  onSelectAddress,
  onInputChange,
  autoFocus = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [usStates, setUsStates] = useState<string[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // US States list
  const statesList = [
    "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut",
    "Delaware", "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa",
    "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan",
    "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire",
    "New Jersey", "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio",
    "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota",
    "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia",
    "Wisconsin", "Wyoming"
  ];

  // Initialize states on component mount
  useEffect(() => {
    setUsStates(statesList);
  }, []);

  // Filter states based on search term
  const filteredStates = usStates.filter(state =>
    state.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Add this effect to update the component when the value prop changes
  useEffect(() => {
    if (value) {
      setSearchTerm('');
      setIsSearching(false);
    }
  }, [value]);

  // Handle input focus
  const handleInputFocus = () => {
    setIsSearching(true);
    setSearchTerm(''); // Clear search term when focusing
    setIsOpen(true);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    setIsOpen(true);
    if (onInputChange) {
      onInputChange(newValue);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsSearching(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelectState = (state: string) => {
    const addressData: AddressData = {
      Line1: '',
      Line2: '',
      City: '',
      State: state,
      PostalCode: '',
      Country: 'United States'
    };

    onSelectAddress(addressData);
    setIsOpen(false);
    setSearchTerm('');
    setIsSearching(false);
  };

  return (
    <div className="relative w-full rounded-xl p-1.5" ref={dropdownRef}>
      <div className="relative flex items-center cursor-pointer">
        <IoLocationOutline className="absolute left-3 top-1.5 text-black text-xl" />
        <input
          type="text"
          placeholder="Search for a state..."
          className="pl-10 pr-4 py-1.5 rounded-md text-black w-full bg-white cursor-pointer outline-none"
          value={isSearching ? searchTerm : value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          autoFocus={autoFocus}
        />
      </div>
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-80 overflow-y-auto">
          <div className="p-2">
            {/* Search Input */}
            <div className="p-2 border-b border-gray-200">
              <input
                type="text"
                placeholder="Search states..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                autoFocus
              />
            </div>

            {/* States List */}
            <ul className="max-h-48 overflow-y-auto">
              {!searchTerm && (
                <li
                  onClick={() => handleSelectState("Select State")}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-500"
                >
                  Select State
                </li>
              )}
              {filteredStates.length > 0 ? (
                filteredStates.map((state) => (
                  <li
                    key={state}
                    onClick={() => handleSelectState(state)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {state}
                  </li>
                ))
              ) : searchTerm ? (
                <li className="px-4 py-2 text-gray-500 text-sm">
                  No states found matching "{searchTerm}"
                </li>
              ) : null}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressDropdown;