// hooks/useRestaurantData.ts
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import {
  getDevicesPatronpal,
  selectDevices,
  selectDeviceLoading,
  selectDeviceError,
} from "../../../redux-store/slices/deviceSlice";
import type { Restaurant } from "../types/types";

// State mapping for common abbreviations and variations
const stateMapping: { [key: string]: string[] } = {
  "Alabama": ["AL", "Alabama"],
  "Alaska": ["AK", "Alaska"],
  "Arizona": ["AZ", "Arizona"],
  "Arkansas": ["AR", "Arkansas"],
  "California": ["CA", "California"],
  "Colorado": ["CO", "Colorado"],
  "Connecticut": ["CT", "Connecticut"],
  "Delaware": ["DE", "Delaware"],
  "Florida": ["FL", "Florida"],
  "Georgia": ["GA", "Georgia"],
  "Hawaii": ["HI", "Hawaii"],
  "Idaho": ["ID", "Idaho"],
  "Illinois": ["IL", "Illinois"],
  "Indiana": ["IN", "Indiana"],
  "Iowa": ["IA", "Iowa"],
  "Kansas": ["KS", "Kansas"],
  "Kentucky": ["KY", "Kentucky"],
  "Louisiana": ["LA", "Louisiana"],
  "Maine": ["ME", "Maine"],
  "Maryland": ["MD", "Maryland"],
  "Massachusetts": ["MA", "Massachusetts"],
  "Michigan": ["MI", "Michigan"],
  "Minnesota": ["MN", "Minnesota"],
  "Mississippi": ["MS", "Mississippi"],
  "Missouri": ["MO", "Missouri"],
  "Montana": ["MT", "Montana"],
  "Nebraska": ["NE", "Nebraska"],
  "Nevada": ["NV", "Nevada"],
  "New Hampshire": ["NH", "New Hampshire"],
  "New Jersey": ["NJ", "New Jersey"],
  "New Mexico": ["NM", "New Mexico"],
  "New York": ["NY", "New York"],
  "North Carolina": ["NC", "North Carolina"],
  "North Dakota": ["ND", "North Dakota"],
  "Ohio": ["OH", "Ohio"],
  "Oklahoma": ["OK", "Oklahoma"],
  "Oregon": ["OR", "Oregon"],
  "Pennsylvania": ["PA", "Pennsylvania"],
  "Rhode Island": ["RI", "Rhode Island"],
  "South Carolina": ["SC", "South Carolina"],
  "South Dakota": ["SD", "South Dakota"],
  "Tennessee": ["TN", "Tennessee"],
  "Texas": ["TX", "Texas"],
  "Utah": ["UT", "Utah"],
  "Vermont": ["VT", "Vermont"],
  "Virginia": ["VA", "Virginia"],
  "Washington": ["WA", "Washington"],
  "West Virginia": ["WV", "West Virginia"],
  "Wisconsin": ["WI", "Wisconsin"],
  "Wyoming": ["WY", "Wyoming"]
};

// Function to check if a device state matches the filter state
const isStateMatch = (deviceState: string, filterState: string): boolean => {
  if (!deviceState || !filterState) return false;

  const deviceStateLower = deviceState.toLowerCase().trim();
  const filterStateLower = filterState.toLowerCase().trim();

  // Direct match
  if (deviceStateLower === filterStateLower) return true;

  // Check against state mapping
  for (const [_fullName, variations] of Object.entries(stateMapping)) {
    const variationsLower = variations.map(v => v.toLowerCase());
    if (variationsLower.includes(filterStateLower) && variationsLower.includes(deviceStateLower)) {
      return true;
    }
  }

  return false;
};

export const useRestaurantData = () => {
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const devices = useSelector(selectDevices);
  const loading = useSelector(selectDeviceLoading);
  const error = useSelector(selectDeviceError);

  const serviceType = searchParams.get("type") || "all";
  const stateFilter = searchParams.get("state");

  useEffect(() => {
    // Only dispatch if we don't have devices data yet
    if (!devices || devices.length === 0) {
      dispatch(getDevicesPatronpal({}) as any);
    }
  }, [dispatch]);

  const transformDeviceToRestaurant = (device: any): Restaurant => ({
    id: device.id ?? device._id,
    _id: device._id,
    name: device.name,
    businessType: device.businessType,
    image: device.image,
    Line1: device.Line1,
    Line2: device.Line2,
    City: device.City,
    State: device.State,
    Phoneno: device.Phoneno,
    PostalCode: device.PostalCode,
    Country: device.Country,
    active: !!device.active,
    userId: device.userId,
    delivery: device.delivery,
    deliveryStartTime: device.deliveryStartTime,
    deliveryEndTime: device.deliveryEndTime,
    ChargesperKm: device.ChargesperKm,
    ChargesFreeKm: device.ChargesFreeKm,
    pickupStartTime: device.pickupStartTime,
    pickupEndTime: device.pickupEndTime,
    reviews: device.reviews || [],
    favorites: device.favorites || [],
  });

  const { filteredDevices, restaurants } = useMemo(() => {
    const transformedRestaurants =
      devices?.map(transformDeviceToRestaurant) || [];
    const activeRestaurants = transformedRestaurants.filter(
      (restaurant) => restaurant.active
    );

    let filtered = activeRestaurants;

    // State filtering
    if (stateFilter && stateFilter !== "Select State") {
      filtered = filtered.filter((restaurant) => {
        return isStateMatch(restaurant.State, stateFilter);
      });
      console.log(`Filtered restaurants by state "${stateFilter}":`, filtered.length);
    }

    // Service type filtering
    switch (serviceType.toLowerCase()) {
      case "delivery":
        filtered = filtered.filter(
          (r) => r.delivery === true || r.delivery === "true"
        );
        break;
      case "pickup":
        filtered = filtered.filter(
          (r) => r.delivery === false || r.delivery === "false"
        );
        break;
      default:
        filtered = filtered.filter(
          (r) =>
            r.delivery === true ||
            r.delivery === "true" ||
            r.delivery === false ||
            r.delivery === "false"
        );
    }

    const filteredDevices = filtered
      .map((restaurant) =>
        devices?.find((device) => device._id === restaurant._id)
      )
      .filter(Boolean);

    return { filteredDevices, restaurants: filtered };
  }, [devices, serviceType, stateFilter]);

  return {
    devices,
    loading,
    error,
    restaurants,
    filteredDevices,
    serviceType,
    stateFilter,
  };
};
